﻿#define DB

using System;
using lep.order;
using Microsoft.Extensions.DependencyInjection;
using AutoMapper;
using lep.freight;
using lep.freight.impl;
using LepCore.Setup;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Http;
using lep.email;
using lep.pricing;
using lep.user;
using lep.run.impl;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Diagnostics;
using lep.job;
using static NHibernate.Cfg.Environment;
using Newtonsoft.Json;
using Microsoft.Extensions.DependencyInjection.Extensions;
using lep.configuration;
using lep.promotion;
using lep.courier;
using System.IO;

namespace LepCore.Test
{
	public class LepFixture : IDisposable
	{
		private ServiceCollection services;
		//private IConfigurationRoot configRoot;
		//private LepAutoMapperProfile lepProfile;
		//private MapperConfiguration config;
		public IMapper Mapper;

		public IServiceScope Scope;

		public IServiceProvider ServiceProvider { get; private set; }
		public IPackageApplication PackageApp { get; private set; }
		public IOrderApplication OrderApp { get; private set; }
		public IJobApplication JobApp { get; private set; }
		public IPricingEngine PricingEngine { get; private set; }
		public IEmailApplication EmailApplication { get; private set; }
		public IPricePointApplication PricePointApplication { get; private set; }
		public IUserApplication UserApp { get; private set; }

		public IPromotionApplication PromoApp { get; private set; }

		public IConfigurationApplication ConfigApp { get; private set; }
		public CartonFinder CartonFinder { get; private set; }

		public RunEngine RunEngine { get; private set; }
		public ICourierApplication CourierApp { get; internal set; }

		public LepFixture()
		{

#if DB
			//lepProfile = new LepAutoMapperProfile(() => _serviceProvider);
			//config = new MapperConfiguration(cfg => { cfg.AddProfile(lepProfile); });
			//mapper = config.CreateMapper();


			var TestProjectDirectory = AppContext.BaseDirectory.Substring(0, AppContext.BaseDirectory.IndexOf("LepCore.Test"));
			var LepCoreDirectory = Path.Combine(TestProjectDirectory, "LepCore");
			var settingsFile = Path.Combine(LepCoreDirectory,
											$@"appsettings.{System.Environment.MachineName}.json");

			var configBuilder = new ConfigurationBuilder()
			.AddJsonFile(settingsFile, optional: true, reloadOnChange: true);
			var configRoot = configBuilder.Build();

			services = new ServiceCollection();
			services.AddSingleton<IConfigurationRoot>(provider => configRoot);
			services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();


			var nhconfig = new NHibernate.Cfg.Configuration();

			nhconfig.SetProperty(Dialect, typeof(NHibernate.Dialect.MsSql2012Dialect).AssemblyQualifiedName);
			nhconfig.SetProperty(ConnectionDriver, typeof(NHibernate.Driver.MicrosoftDataSqlClientDriver).AssemblyQualifiedName);
			nhconfig.SetProperty(Isolation, "ReadCommitted");
			nhconfig.SetProperty(PrepareSql, "true");
			nhconfig.SetProperty(UseSecondLevelCache, "true");
			nhconfig.SetProperty(UseQueryCache, "true");

			nhconfig.SetProperty(Hbm2ddlAuto, "none");
			nhconfig.SetProperty(Hbm2ddlKeyWords, "none");

			var connstr = configRoot["Nhibernate:Con"];
			nhconfig.SetProperty(ConnectionString, connstr);
			nhconfig.SetProperty(CommandTimeout, "600");

			// call session context works for both HangFire and Web
			nhconfig.SetProperty(CurrentSessionContextClass, "call");

			nhconfig.SetProperty(DefaultBatchFetchSize, "128");
			nhconfig.SetProperty(BatchSize, "50");

			nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, "false");
			nhconfig.SetProperty(NHibernate.Cfg.Environment.FormatSql, "false");
			//nhConfigurationCache.SetProperty(Environment.CurrentSessionContextClass, typeof(UnitTestSessionContext).AssemblyQualifiedName);

			nhconfig.AddAssembly(typeof(ICustomerUser).Assembly);

			//config.AppendListeners(NHibernate.Event.ListenerType.PostUpdate, new[] { new AuditEventListener() });


			nhconfig.AppendListeners(NHibernate.Event.ListenerType.PostUpdate, new[] { new NhAuditEventListener() });
			nhconfig.AppendListeners(NHibernate.Event.ListenerType.PostInsert, new[] { new NhAuditEventListener() });
			nhconfig.AppendListeners(NHibernate.Event.ListenerType.PostDelete, new[] { new NhAuditEventListener() });

			//nhconfig.AppendListeners(NHibernate.Event.ListenerType.Flush, new[] { new AuditEventListener() });

			////config.SetInterceptor(new NoUpdateInterceptor());
			foreach (NHibernate.Mapping.PersistentClass persistentClass in nhconfig.ClassMappings)
			{
				persistentClass.DynamicUpdate = true;
			}


			services.AddSingleton<NHibernate.Cfg.Configuration>(nhconfig);
			services.AddSingleton<NHibernate.ISessionFactory>(s => s.GetRequiredService<NHibernate.Cfg.Configuration>().BuildSessionFactory());
			services.AddScoped(s => s.GetRequiredService<NHibernate.ISessionFactory>()
				.WithOptions().Interceptor(new NHDIInterceptor(s)).OpenSession());


			//==================================================






			var settings = new JsonSerializerSettings
			{
				ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
				Formatting = Formatting.Indented,
				DateTimeZoneHandling = DateTimeZoneHandling.Local,
			};
			
			var serializer = JsonSerializer.Create(settings);
			services.Add(new ServiceDescriptor(typeof(JsonSerializer), provider => serializer, ServiceLifetime.Singleton));




			services.AddLepDependencyInjection(configRoot);


			var lepProfile = new LepAutoMapperProfile(() => ServiceProvider);
			var configAm = new MapperConfiguration(cfg => cfg.AddProfile(lepProfile));
			var mapper = configAm.CreateMapper();

			services.AddSingleton<IMapper>(mapper);




			ServiceProvider = services.BuildServiceProvider();
			Scope = ServiceProvider.CreateScope();
			
			
			PackageApp = Scope.ServiceProvider.GetRequiredService<IPackageApplication>();
			OrderApp = Scope.ServiceProvider.GetRequiredService<IOrderApplication>();
			JobApp = Scope.ServiceProvider.GetRequiredService<IJobApplication>();
			EmailApplication   = Scope.ServiceProvider.GetRequiredService<IEmailApplication>();

			PricingEngine = Scope.ServiceProvider.GetRequiredService<IPricingEngine>();
			PricePointApplication = Scope.ServiceProvider.GetRequiredService<IPricePointApplication>();


			UserApp = Scope.ServiceProvider.GetRequiredService<IUserApplication>();
			RunEngine = Scope.ServiceProvider.GetRequiredService<RunEngine>();

			ConfigApp = Scope.ServiceProvider.GetRequiredService<IConfigurationApplication>();

			PromoApp = Scope.ServiceProvider.GetRequiredService<IPromotionApplication>();
			Mapper = Scope.ServiceProvider.GetRequiredService<IMapper>();
#endif
		}


		public void Dispose()
		{
			Scope?.Dispose();
			Scope = null;


			ServiceProvider = null;

			services = null;
			//configRoot = null;
			//lepProfile = null;
			//config = null;
			Mapper = null;
			PackageApp = null;
			GC.Collect();
			GC.WaitForPendingFinalizers();

		}



	}
}
