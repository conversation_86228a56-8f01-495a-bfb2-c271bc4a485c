# LepCore Publishing Guide

This directory contains scripts to publish LepCore to `c:\lepsf\upd` using the **FolderProfile** publish profile.

## 📁 Available Scripts

### 1. `quick-publish.bat` - Simple & Fast
**Best for:** Quick deployments during development

```batch
quick-publish.bat
```

- **Minimal output** - just runs the publish command
- **No confirmations** - publishes immediately
- **Fast execution** - no extra checks or features

### 2. `publish-to-upd.bat` - Full Featured Batch
**Best for:** Production deployments with detailed logging

```batch
publish-to-upd.bat
```

**Features:**
- ✅ **Detailed progress output** with timestamps
- ✅ **Error checking** and helpful error messages
- ✅ **Directory validation** and creation
- ✅ **Success/failure reporting** with file listing
- ✅ **Comprehensive logging** of the publish process

### 3. `publish-to-upd.ps1` - Advanced PowerShell
**Best for:** Advanced users who need maximum control

```powershell
# Basic usage
.\publish-to-upd.ps1

# With options
.\publish-to-upd.ps1 -Verbose -Force
.\publish-to-upd.ps1 -SkipBuild
```

**Parameters:**
- `-Force` - Skip confirmation prompts
- `-<PERSON><PERSON><PERSON>e` - Enable detailed output
- `-Ski<PERSON>Build` - Skip building, just publish

**Features:**
- 🎨 **Colored output** for better readability
- ⚠️ **Safety checks** with confirmation prompts
- 📊 **Performance timing** and detailed reporting
- 🔍 **Advanced error handling** with troubleshooting tips
- 📋 **File listing** before and after publish

## 🎯 Publish Profile Details

**Profile:** `FolderProfile.pubxml`
- **Target:** `c:\lepsf\upd`
- **Configuration:** Release
- **Framework:** net8.0-windows7.0
- **Runtime:** win-x64
- **Self-contained:** false
- **Delete existing files:** true

## 🚀 Quick Start

1. **For quick development deploys:**
   ```batch
   quick-publish.bat
   ```

2. **For production deploys:**
   ```batch
   publish-to-upd.bat
   ```

3. **For advanced control:**
   ```powershell
   .\publish-to-upd.ps1 -Verbose
   ```

## 📋 Prerequisites

- **.NET 8.0 SDK** installed
- **PowerShell** (for .ps1 script)
- **Write permissions** to `c:\lepsf\upd`
- **LepCore project** must build successfully

## 🔧 Troubleshooting

### Common Issues:

1. **Build Errors**
   ```
   Solution: Fix compilation errors first
   Command: dotnet build LepCore.csproj
   ```

2. **Permission Denied**
   ```
   Solution: Run as Administrator or check folder permissions
   ```

3. **Target Directory Issues**
   ```
   Solution: Ensure c:\lepsf\upd exists and is writable
   ```

4. **Frontend Build Errors**
   ```
   Solution: Check gulp/npm dependencies in FrontEnd folder
   ```

### Getting Help:

- **Verbose output:** Use `publish-to-upd.ps1 -Verbose`
- **Manual publish:** `dotnet publish LepCore.csproj /p:PublishProfile=FolderProfile`
- **Check logs:** Look for detailed error messages in the output

## 📁 File Structure After Publish

```
c:\lepsf\upd\
├── LepCore.exe
├── LepCore.dll
├── appsettings.json
├── web.config
├── wwwroot/
│   ├── css/
│   ├── js/
│   └── ...
└── [other runtime files]
```

## ⚙️ Customization

To modify the publish behavior:
1. Edit `Properties/PublishProfiles/FolderProfile.pubxml`
2. Update the scripts if needed
3. Test with `quick-publish.bat` first

## 🔄 Integration with CI/CD

These scripts can be integrated into automated deployment pipelines:

```batch
REM In your CI/CD script
cd C:\LepSF\au\LepCore
call quick-publish.bat
if %ERRORLEVEL% NEQ 0 exit /b %ERRORLEVEL%
```

---

**Last Updated:** 2025-01-07  
**LepCore Version:** net8.0-windows7.0
