using AutoMapper;
using lep;
using lep.email;
using lep.order;
using lep.user;
using lep.user.impl;
using LepCore.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using NHibernate.Criterion;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace LepCore.Controllers
{
	/// <summary>
	/// Controller for Customers to  manage sub customers
	/// </summary>
	[Produces("application/json")]
	[Route("api/Cust/SubCustomer")]
	[Authorize(Roles = LepRoles.Customer)]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class CustomersCustomerController : Controller
	{
		private readonly IMapper _mapper;
		private readonly IUserApplication _userApplication;
		private readonly IEmailApplication _emailApplication;
		private NHibernate.ISession _session;
		private readonly IHttpContextAccessor _contextAccessor;
		private ICustomerUser _currentUser;

		public CustomersCustomerController(
			IHttpContextAccessor contextAccessor,
			IUserApplication userApplication,
			IEmailApplication emailApplication,
			IMapper mapper,
			NHibernate.ISession session
		)
		{
			_contextAccessor = contextAccessor;
			_userApplication = userApplication;
			_emailApplication = emailApplication;
			_mapper = mapper;
			_session = session;
		}

		[ApiExplorerSettings(IgnoreApi = true)]
		public override void OnActionExecuting(ActionExecutingContext context)
		{
			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			_currentUser = (ICustomerUser)_userApplication.GetUser(userId);
			if (_currentUser is null || !_currentUser.IsPrintPortalEnabled)
			{
				throw new Exception("Please contact system admin");
			}
		}

		//called from staff pages to get a list of orders
	    [HttpGet("")]
		[Produces(typeof(PagedResult<CustomerListDTO>))]
		public IActionResult GetAll([FromQuery][Required] CustomerSearchCriteria sp, [FromServices] NHibernate.ISession session)
		{
			var customer = "";
			if (!string.IsNullOrEmpty(sp.Customer))
			{
				customer = sp.Customer.Trim();
			}

			//var customerid = 0;
			//if (!string.IsNullOrEmpty(sp.Customer))
			//{
			//	int.TryParse(sp.Customer.Trim(), out customerid);
			//}



			Order order = null;
			if (!string.IsNullOrEmpty(sp.SortField))
			{
				// FIX: Map CustomerNr sorting to Id sorting since CustomerNr is a computed property
				var sortField = sp.SortField == "CustomerNr" ? "Id" : sp.SortField;
				order = new Order(sortField, sp.SortDir == "true");
			}
			else
			{
				order = new Order("Id", true);
			}

			var criteria = _userApplication.CustomerCriteria(customer, 0, sp.OrderNr, sp.JobNr,sp.SystemAccess,
				sp.PaymentTerms, sp.IsPrintPortalEnabled, sp.ShowArchived);


			if (!sp.ShowArchived)
			{
				criteria.Add(Restrictions.Eq("cust.Archived", false));
			}

			criteria.Add(Restrictions.Eq("ParentCustomer", _currentUser));

			var sortOrder = new[] { order };

			var list = Utils.GetPagedResult2(session, criteria, sp.Page, 20, x => new CustomerListDTO(x), sortOrder);

			return new OkObjectResult(list);
		}

		[HttpGet("{id:int}")]
		[Produces(typeof(CustomerUserDto))]
		public IActionResult Get(int id)
		{
			ICustomerUser customer = null;

			if (id == 0)
			{
				customer = (lep.user.impl.CustomerUser)_userApplication.NewCustomerUser();
				customer.Contacts.Add(new lep.contact.impl.Contact());
			}
			else
			{
				customer = _userApplication.GetCustomerUser(id);
				customer.LEPOnlineBalance = _userApplication.GetCustomerLEPOnlineBalance(id);
				customer.AvaiableBalance = _userApplication.GetCustomerAvailableBalance(id);
			}

			if (customer == null) return NotFound();

			var result = _mapper.Map<CustomerUserDto>(customer);
			return new OkObjectResult(result);
		}

		[HttpPut("{id:int}")]
		public IActionResult PutCustomer(int id, [FromBody] [Required] CustomerUserDto dto)
		{
			var customer = _userApplication.GetCustomerUser(dto.Id);
			if (customer == null)
			{
				customer = _userApplication.NewCustomerUser();
				customer.Username = dto.Username;
				var password = dto.Password ?? Utils.GeneratePassword(10);
				_userApplication.SetPassword(customer, password);
			}

			customer = _mapper.Map(dto, customer);
			customer.IsEnabled = true;
			customer.PreferredCourier = new lep.courier.CourierType(lep.courier.CourierType.None);
			customer.ParentCustomer = _currentUser;

			_userApplication.Save(customer);
			Response.Headers.Add("Id", customer.Id.ToString());
			return new StatusCodeResult(id == 0 ? StatusCodes.Status201Created : StatusCodes.Status204NoContent);
		}

		[HttpPatch("{id:int}")]
		public IActionResult PatchCustomer(int id, [FromBody] [Required] JsonPatchDocument<CustomerUser> patchDoc)
		{
			var customer = (CustomerUser)_userApplication.GetCustomerUser(id);
			patchDoc.ApplyTo(customer);
			_userApplication.Save(customer);
			return Ok();
		}

		[HttpDelete("{id:int}")]
		public IActionResult Delete(int id) //int? Id = null
		{
			var customer = _userApplication.GetUser(id);
			if (customer == null) return NotFound();
			_userApplication.Delete(customer);
			return Ok();
		}

		[HttpPost("{id:int}/archive")]
		public IActionResult Archive(int id) //int? Id = null
		{
			var customer = (ICustomerUser)_userApplication.GetUser(id);
			if (customer == null) return NotFound();
			customer.Archived = true;
			_userApplication.Save(customer);
			return Ok();
		}

		[HttpPost("{id:int}/unarchive")]
		public IActionResult UnArchive(int id) //int? Id = null
		{
			var customer = (ICustomerUser)_userApplication.GetUser(id);
			if (customer == null) return NotFound();
			customer.Archived = false;
			_userApplication.Save(customer);
			return Ok();
		}

		[HttpPost("IsValidUsername")]
		public IActionResult IsUniqueMYOB(int userId, string userName)
		{
			var user = _userApplication.GetUser(userId);
			var valid = _userApplication.IsValidUsername(userName, user);
			return new OkObjectResult(new { Valid = valid });
		}

		[HttpPost("{Id:int}/password")]
		public IActionResult PasswordReset([FromRoute] int id, [FromBody]  string password)
		{
			var user = _userApplication.GetUser(id);
			if (user == null) return BadRequest();
			_userApplication.SetPassword(user, password);
			_userApplication.Save(user);
			return Ok();
		}

		[HttpGet("SubCustomers")]
		public IActionResult GetSubCustomers([FromQuery] string s)
		{
			var nameFilter = "";

			if (!string.IsNullOrEmpty(s))
			{
				nameFilter = $" and Name like '%{s}%' ";
			}
			var q = _session.CreateSQLQuery($"select Id, Name from Customer where ParentCustomerId  = {_currentUser.Id} {nameFilter} Order by 2")
				.List<object[]>()
				.Select(x => new { Id = (int)x[0], Name = (string)x[1] });

			return new OkObjectResult(q);
		}

		[HttpGet("{id:int}/newOrder")]
		public IActionResult BlankJobForSubCustomer([FromRoute] int id, [FromServices] IOrderApplication _orderApp)
		{
			IOrder order = _orderApp.NewOrder((ICustomerUser)_currentUser);
			var subCustomer = (ICustomerUser)_userApplication.GetUser(id);
			order.WLCustomerId = id;
			order.DeliveryAddress = subCustomer.PostalAddress;
			order.WLContact = subCustomer.Contact1;

			order.PaymentStatus = OrderPaymentStatusOptions.AwaitingPayment;
			order.FreightPriceCode = "";
			order.IsWLOrder = true;
			_orderApp.BaseSave(order);

			return new OkObjectResult(new { orderId = order.Id });
		}
	}
}
