<Query Kind="Program">
  <Reference Relative="AutoMapper.dll">C:\LINQPad5\AutoMapper.dll</Reference>
  <Reference Relative="FastReport.dll">C:\LINQPad5\FastReport.dll</Reference>
  <Reference Relative="lep.dll">C:\LINQPad5\lep.dll</Reference>
  <Reference Relative="LepCore.DTOs.dll">C:\LINQPad5\LepCore.DTOs.dll</Reference>
  <Reference Relative="lumen.dll">C:\LINQPad5\lumen.dll</Reference>
  <Reference Relative="NHibernate.dll">C:\LINQPad5\NHibernate.dll</Reference>
  <Reference Relative="Remotion.Linq.dll">C:\LINQPad5\Remotion.Linq.dll</Reference>
  <Reference Relative="Remotion.Linq.EagerFetching.dll">C:\LINQPad5\Remotion.Linq.EagerFetching.dll</Reference>
  <NuGetReference>MYOB.AccountRight.API.SDK</NuGetReference>
  <Namespace>AutoMapper</Namespace>
  <Namespace>FastReport</Namespace>
  <Namespace>FastReport.Export.Html</Namespace>
  <Namespace>FastReport.Export.Pdf</Namespace>
  <Namespace>lep</Namespace>
  <Namespace>lep.address</Namespace>
  <Namespace>lep.address.impl</Namespace>
  <Namespace>lep.backupdelete</Namespace>
  <Namespace>lep.backupdelete.impl</Namespace>
  <Namespace>lep.barcode</Namespace>
  <Namespace>lep.barcode.impl</Namespace>
  <Namespace>lep.CompData</Namespace>
  <Namespace>lep.configuration</Namespace>
  <Namespace>lep.configuration.impl</Namespace>
  <Namespace>lep.contact</Namespace>
  <Namespace>lep.contact.impl</Namespace>
  <Namespace>lep.content</Namespace>
  <Namespace>lep.content.impl</Namespace>
  <Namespace>lep.courier</Namespace>
  <Namespace>lep.courier.impl</Namespace>
  <Namespace>lep.cron</Namespace>
  <Namespace>lep.cron.impl</Namespace>
  <Namespace>lep.despatch</Namespace>
  <Namespace>lep.despatch.impl</Namespace>
  <Namespace>lep.despatch.impl.label</Namespace>
  <Namespace>lep.email</Namespace>
  <Namespace>lep.email.impl</Namespace>
  <Namespace>lep.extensionmethods</Namespace>
  <Namespace>lep.freight</Namespace>
  <Namespace>lep.freight.csv</Namespace>
  <Namespace>lep.freight.impl</Namespace>
  <Namespace>lep.job</Namespace>
  <Namespace>lep.job.csv</Namespace>
  <Namespace>lep.job.impl</Namespace>
  <Namespace>lep.job.printing</Namespace>
  <Namespace>lep.jobmonitor</Namespace>
  <Namespace>lep.jobmonitor.impl</Namespace>
  <Namespace>lep.lepcrm</Namespace>
  <Namespace>lep.macro</Namespace>
  <Namespace>lep.macro.impl</Namespace>
  <Namespace>lep.onlineIVRTxn</Namespace>
  <Namespace>lep.onlineIVRTxn.impl</Namespace>
  <Namespace>lep.onlineTxn</Namespace>
  <Namespace>lep.onlineTxn.impl</Namespace>
  <Namespace>lep.order</Namespace>
  <Namespace>lep.order.impl</Namespace>
  <Namespace>lep.pricing</Namespace>
  <Namespace>lep.pricing.csv</Namespace>
  <Namespace>lep.pricing.impl</Namespace>
  <Namespace>lep.promotion</Namespace>
  <Namespace>lep.promotion.impl</Namespace>
  <Namespace>lep.run</Namespace>
  <Namespace>lep.run.impl</Namespace>
  <Namespace>lep.security</Namespace>
  <Namespace>lep.src</Namespace>
  <Namespace>lep.src.onlineTxn.impl.Westpac</Namespace>
  <Namespace>lep.user</Namespace>
  <Namespace>lep.user.impl</Namespace>
  <Namespace>lep.www</Namespace>
  <Namespace>LepCore.Dto</Namespace>
  <Namespace>myob =  MYOB.AccountRight.SDK.Services</Namespace>
  <Namespace>MYOB.AccountRight.SDK</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Contracts</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Contracts.Version2</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Contracts.Version2.Contact</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Contracts.Version2.GeneralLedger</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Contracts.Version2.Inventory</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Contracts.Version2.Sale</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Services</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Services.Contact</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Services.GeneralLedger</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Services.Inventory</Namespace>
  <Namespace>MYOB.AccountRight.SDK.Services.Sale</Namespace>
  <Namespace>Newtonsoft.Json</Namespace>
  <Namespace>NHibernate</Namespace>
  <Namespace>NHibernate.Cfg</Namespace>
  <Namespace>NHibernate.Linq</Namespace>
  <Namespace>NHibernate.SqlCommand</Namespace>
  <Namespace>NHibernate.Transform</Namespace>
  <Namespace>RoundedRectangles</Namespace>
  <Namespace>System.Collections.Generic</Namespace>
  <Namespace>System.Data.SqlTypes</Namespace>
  <Namespace>System.Linq</Namespace>
  <Namespace>System.Net</Namespace>
  <Namespace>System.Net.Mail</Namespace>
  <Namespace>System.Text.RegularExpressions</Namespace>
  <Namespace>System.Web</Namespace>
</Query>

const int INVOICE_BATCH_SIZE = 100;
const int REFUND_BATCH_SIZE = 10;


// /*LOCAL*/ string LEPConnectionString = "Data Source=.; user id=lepcore; password=********$%MpHU91; Initial Catalog=PRD_AU_2020_06_01";
/*LIVE*/
string LEPConnectionString = "Data Source=SRV03;user id=sa; password=*************; Initial Catalog=PRD_AU";
//   /*TEST*/ string LEPConnectionString = "Data Source=NEWMAN;user id=sa; password=*************; Initial Catalog=PRD_AU_2020_06_18_21_50";
const string format = "yyyy-MM-dd HH:mm:ss";
//string DataDirectoryFullName = @"\\icemedia\C$\lepdata";

string DataDirectoryFullName = @"\\dfs01\resource";
//string pdfFolder = @"C:\LepData\Invoices2"
string pdfFolder = @"\\dfs01\resource\invoices";

bool createOrderInvoice = true;
bool createRefundInvoice = true;

bool createPdfInvoice = true;
bool emailPdfInvoice = false;
//string emailPdfAddress = "<EMAIL>";
string emailPdfAddress = null;
SqlConnection lepConn;

NHibernate.ISession LepSession = null;

// myob

void Main()
{



	FastReport.Utils.Config.FontListFolder = "C:\\LEPDATA\\FONTS";
	FastReport.Utils.Config.ReportSettings.ShowProgress = false;

	#region Setup LEP DL
	var nhconfig = new NHibernate.Cfg.Configuration();
	nhconfig.SetProperty(NHibernate.Cfg.Environment.Dialect, typeof(NHibernate.Dialect.MsSql2012Dialect).AssemblyQualifiedName);
	nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionDriver, typeof(NHibernate.Driver.MicrosoftDataSqlClientDriver).AssemblyQualifiedName);
	nhconfig.SetProperty(NHibernate.Cfg.Environment.Isolation, "ReadCommitted");
	nhconfig.SetProperty(NHibernate.Cfg.Environment.UseSecondLevelCache, false.ToString());
	nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, false.ToString());
	nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionProvider, typeof(NHibernate.Connection.DriverConnectionProvider).AssemblyQualifiedName);
	nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionString, LEPConnectionString);
	nhconfig.SetProperty(NHibernate.Cfg.Environment.CurrentSessionContextClass, "www");
	nhconfig.SetProperty(NHibernate.Cfg.Environment.BatchSize, "1000");
	//nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, "true");
	//nhconfig.SetProperty(NHibernate.Cfg.Environment.FormatSql, "true");
	nhconfig.AddAssembly(typeof(ICustomerUser).Assembly);
	var factory = nhconfig.BuildSessionFactory();
	LepSession = factory.OpenSession();
	#endregion



	#region automapper

	var lepProfile = new LepCore.Setup.LepAutoMapperProfile(() => null);
	var config = new MapperConfiguration(cfg => { cfg.AddProfile(lepProfile); });


	var _mapper = config.CreateMapper();

	Mapper.Initialize(cfg =>
	{
		cfg.AddProfile(lepProfile);
	});

	#endregion



	var lepSqls = new StringBuilder();
	var ordersInvoiced = new List<int>();
	var ordersListToInvoice = LepSession.Query<IOrder>().Fetch(x => x.Jobs)

				 .Where(o => o.Invoiced2 == "Y" && o.Id < 1086958)
				 
			  .OrderByDescending(o => o.Id)
			  .Select(o => new KeyValuePair<int, string>(o.Id, o.Customer.Username))
					 .ToList();

	Console.WriteLine("Found " + ordersListToInvoice.Count().ToString() + " orders");


	if (!ordersListToInvoice.Any())
	{
		return;
	}




	var errorMsg = "";
	foreach (var oId in ordersListToInvoice)
	{
		var o = LepSession.Query<IOrder>().Where(_ => _.Id == oId.Key).First();


		if (createPdfInvoice)
		{
			var orderDto = _mapper.Map<OrderInvoiceDto>(o);
			var jsonStr = JsonConvert.SerializeObject(orderDto, Newtonsoft.Json.Formatting.Indented);

			jsonStr = jsonStr.Replace("'", "''");
			//jsonStr.Dump();
			Report reportOrderInvoice = new FastReport.Report();
			var fileName = @"C:\LepData\Labels2\lep-invoice-order.frx";
			reportOrderInvoice.Load(fileName);

			reportOrderInvoice.Dictionary.Connections[0].ConnectionString = "Json='" + jsonStr + "'";
			reportOrderInvoice.Prepare();
			var d = o.FinishDate.Value.Date.ToString("yyyy/MMM/dd");

			var dir = $@"{pdfFolder}\{d}";
			if (!Directory.Exists(dir))
				Directory.CreateDirectory(dir);

			var fileDownloadName = $@"{dir}/O{o.Id}.pdf";
			var pdfExport = new PDFExport();
			var htmlExport = new HTMLExport();

			pdfExport.AllowModify = true;

			using (var fs1 = new System.IO.FileStream(fileDownloadName, FileMode.Create))
			{
				pdfExport.Export(reportOrderInvoice, fs1);
			};


			var orderExtraFolder = $@"{DataDirectoryFullName}\orders\{o.DateCreated:yyyyMMdd}\{o.OrderNr}\Extrafiles";

			if (!Directory.Exists(orderExtraFolder))
				Directory.CreateDirectory(orderExtraFolder);
			var y = Path.Combine(orderExtraFolder, $"O{o.Id}.pdf");
			y.Dump();

			File.Copy(fileDownloadName, y, true);


			//				if (emailPdfInvoice)
			//				{
			//
			//					var address = o.Customer.AccountEmail;
			//
			//					if (string.IsNullOrEmpty(address))
			//					{
			//						address = o.Customer.Email;
			//					}
			//
			//					if (!string.IsNullOrEmpty(emailPdfAddress))
			//					{
			//						address = emailPdfAddress;
			//					}
			//
			//					if (!string.IsNullOrEmpty(address))
			//					{
			//						var mail = createEmail();
			//						mail.Subject = $"Invoice {invoiceNumber}";
			//
			//
			//						mail.To.Add(new MailAddress(address));
			//						mail.Attachments.Add(new Attachment(fileDownloadName));
			//
			//						mail.IsBodyHtml = true;
			//						var body = $@"Please find attached your invoice {invoiceNumber} from LEP Colour Printers.<br/>
			//										 Contact <EMAIL> for any queries.";
			//						mail.Body = body;
			//						emailer.Send(mail);
			//					}
			//				}
		}
	}









	LepSession.Flush();
	LepSession.Close();
}