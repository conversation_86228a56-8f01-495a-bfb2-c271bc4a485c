using lep;
using lep.user;
using Microsoft.Extensions.Configuration;
using NHibernate;
using NHibernate.Cfg;
using NHibernate.Event;
using NHibernate.Mapping;
using System;
using System.Threading;
using System.Threading.Tasks;
using static NHibernate.Cfg.Environment;

namespace LepCore.Bench
{
	public class AppSessionFactory
	{
		public Configuration Configuration { get; }
		public ISessionFactory SessionFactory { get; }

		//Microsoft.Extensions.Logging.ILoggerFactory loggerFactory,, IHostingEnvironment _env
		public AppSessionFactory(IConfigurationRoot configurationRoot)
		{
			//if (LepGlobal.Instance.TestBox)
			//{
			//	NHibernateProfiler.Initialize();
			//}
			//NHibernateProfiler.Initialize();
			//NHibernate.NHibernateLogger.SetLoggersFactory(new NHibernateToMicrosoftLoggerFactory(loggerFactory));
			//NHibernateLogger.SetLoggersFactory(new NHibernate.Logging.Serilog.SerilogLoggerFactory());

			var config = new Configuration();

			config.SetProperty(Dialect, typeof(NHibernate.Dialect.MsSql2012Dialect).AssemblyQualifiedName);
			config.SetProperty(ConnectionDriver, typeof(NHibernate.Driver.SqlClientDriver).AssemblyQualifiedName);
			config.SetProperty(Isolation, "ReadCommitted");
			config.SetProperty(PrepareSql, "true");
			//config.SetProperty(CacheProvider, typeof(NHibernate.Caches.SysCache2.SysCacheProvider).AssemblyQualifiedName);
			config.SetProperty(UseSecondLevelCache, "true");
			config.SetProperty(UseQueryCache, "true");

			config.SetProperty(Hbm2ddlAuto, "none");

			var connstr = configurationRoot["Nhibernate:Con"];
			config.SetProperty(ConnectionString, connstr);
			config.SetProperty(CommandTimeout, "600");

			// call session context works for both HangFire and Web
			config.SetProperty(CurrentSessionContextClass, "call");

			config.SetProperty(DefaultBatchFetchSize, "128");
			config.SetProperty(BatchSize, "50");

			config.SetProperty(NHibernate.Cfg.Environment.ShowSql, "true");
			config.SetProperty(NHibernate.Cfg.Environment.FormatSql, "true");
			//config.SetProperty(Environment.CurrentSessionContextClass, typeof(UnitTestSessionContext).AssemblyQualifiedName);

			config.AddAssembly(typeof(ICustomerUser).Assembly);

			//config.AppendListeners(NHibernate.Event.ListenerType.PostUpdate, new[] { new AuditEventListener() });
			config.AppendListeners(NHibernate.Event.ListenerType.PreUpdate, new[] { new AuditEventListener() });
			config.AppendListeners(NHibernate.Event.ListenerType.PreInsert, new[] { new AuditEventListener() });

			////config.SetInterceptor(new NoUpdateInterceptor());
			foreach (NHibernate.Mapping.PersistentClass persistentClass in config.ClassMappings)
			{
				persistentClass.DynamicUpdate = true;
			}

			//config.Configure();

			config.SessionFactory().GenerateStatistics();
			SessionFactory = config.BuildSessionFactory();
		}

		public ISession OpenSession()
		{
			return SessionFactory.OpenSession();
		}
	}


	internal class AuditEventListener : IPreUpdateEventListener, IPreInsertEventListener
	{
		public bool OnPreUpdate(PreUpdateEvent e)
		{
			//UpdateAuditTrail(e.State, e.Persister.PropertyNames, (IEntity)e.Entity);
			return false;
		}
		public bool OnPreInsert(PreInsertEvent e)
		{
			//UpdateAuditTrail(e.State, e.Persister.PropertyNames, (IEntity)e.Entity);
			return false;
		}
		//private void UpdateAuditTrail(object[] state, string[] names, IEntity entity)
		//{
		//	var idx = System.Array.FindIndex(names, n => n == "UpdatedBy");
		//	state[idx] = string.IsNullOrEmpty(Thread.CurrentPrincipal.Identity.Name) ? "Unknown" : Thread.CurrentPrincipal.Identity.Name;
		//	entity.UpdatedBy = state[idx].ToString();
		//	idx = System.Array.FindIndex(names, n => n == "UpdatedTimestamp");
		//	DateTime now = DateTime.Now;
		//	state[idx] = now;
		//	entity.UpdatedTimestamp = now;
		//}

		public Task<bool> OnPreUpdateAsync(PreUpdateEvent @event, CancellationToken cancellationToken)
		{
			throw new System.NotImplementedException();
		}

		public Task<bool> OnPreInsertAsync(PreInsertEvent @event, CancellationToken cancellationToken)
		{
			throw new System.NotImplementedException();
		}
	}



	//public class NHibernateToMicrosoftLogger : INHibernateLogger
	//{
	//	private readonly ILogger _msLogger;

	//	public NHibernateToMicrosoftLogger(ILogger msLogger)
	//	{
	//		_msLogger = msLogger ?? throw new ArgumentNullException(nameof(msLogger));
	//	}

	//	private static readonly Dictionary<NHibernateLogLevel, LogLevel> MapLevels = new Dictionary<NHibernateLogLevel, LogLevel>
	//	{
	//		{ NHibernateLogLevel.Trace, LogLevel.Trace },
	//		{ NHibernateLogLevel.Debug, LogLevel.Debug },
	//		{ NHibernateLogLevel.Info, LogLevel.Information },
	//		{ NHibernateLogLevel.Warn, LogLevel.Warning },
	//		{ NHibernateLogLevel.Error, LogLevel.Error },
	//		{ NHibernateLogLevel.Fatal, LogLevel.Critical },
	//		{ NHibernateLogLevel.None, LogLevel.None },
	//	};

	//	public void Log(NHibernateLogLevel logLevel, NHibernateLogValues state, Exception exception)
	//	{
	//		_msLogger.Log(MapLevels[logLevel], 0, new FormattedLogValues(state.Format, state.Args), exception, MessageFormatter);
	//	}

	//	public bool IsEnabled(NHibernateLogLevel logLevel)
	//	{
	//		return _msLogger.IsEnabled(MapLevels[logLevel]);
	//	}

	//	private static string MessageFormatter(object state, Exception error)
	//	{
	//		return state.ToString();
	//	}
	//}

	//public class NHibernateToMicrosoftLoggerFactory : INHibernateLoggerFactory
	//{
	//	private readonly Microsoft.Extensions.Logging.ILoggerFactory _loggerFactory;

	//	public NHibernateToMicrosoftLoggerFactory(Microsoft.Extensions.Logging.ILoggerFactory loggerFactory)
	//	{
	//		_loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
	//	}

	//	public INHibernateLogger LoggerFor(string keyName)
	//	{
	//		var msLogger = _loggerFactory.CreateLogger(keyName);
	//		return new NHibernateToMicrosoftLogger(msLogger);
	//	}

	//	public INHibernateLogger LoggerFor(System.Type type)
	//	{
	//		return LoggerFor(
	//			Microsoft.Extensions.Logging.Abstractions.Internal.TypeNameHelper.GetTypeDisplayName(type));
	//	}
	//}
}
