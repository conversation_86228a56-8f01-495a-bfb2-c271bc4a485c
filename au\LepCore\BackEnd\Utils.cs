using AutoMapper;
using LepCore.Dto;
using NHibernate;
using NHibernate.Criterion;
using NHibernate.Impl;
using NHibernate.Loader.Criteria;
using NHibernate.Persister.Entity;
using NHibernate.Transform;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace LepCore
{
	public static class LepRoles
	{
		public const string Staff = "Staff";


		public const string SuperAdministrator = "SuperAdministrator";
		public const string Administrator = "Administrator";
		public const string Accounts = "Accounts";
		public const string System = "System";

		public const string Prepress = "Prepress";
		public const string PrepressFinishing = "PrepressFinishing";

		public const string Scanner = "Scanner";



		public const string Customer = "Customer";
		public const string AnonymousWLCustomer = "AnonymousWLCustomer";
		public const string LoggedInWLCustomer = "LoggedInWLCustomer";

		//Accounts,
		//[Description("Customer Service")]
		//CustomerService,
		//Administrator,
		//[Description("Marketing Specialist")]
		//MarketingSpecialist,
		//,
		//Packing,
		//Dispatch,
		//Invoicing
	}


	public static class Exts
	{
		//public static ResponseResult<List<Controllers.IdAndName<T>>> ToListOfIdAndName<T> (this IEnumerable<T> list) where T : IIdAndName
		//{
		//    var result = list.Select(m => new Controllers.IdAndName<T>() {
		//        Id = m.Id,
		//        Name = m.Name
		//    }).ToList();
		//    return new ResponseResult<List<Controllers.IdAndName<T>>>() { Data = result };
		//}
	}


	public static class ReferenceExtensions
	{
		public static TOut IfNotNull<TIn, TOut>(this TIn v, Func<TIn, TOut> f)
										where TIn : class
										where TOut : class
		{
			if (v == null)
				return null;

			return f(v);
		}
	}



	public static class Utils
	{
		public static Func<A, R> Memoize<A, R>(this Func<A, R> f)
		{
			var map = new Dictionary<A, R>();
			return a =>
			{
				R value;
				if (map.TryGetValue(a, out value))
					return value;
				value = f(a);
				map.Add(a, value);
				return value;
			};
		}





		//public static string GetGeneratedSql(ICriteria criteria)
		//{
		//	var criteriaImpl = (CriteriaImpl)criteria;
		//	var sessionImpl = (SessionImpl)criteriaImpl.Session;
		//	var factory = (SessionFactoryImpl)sessionImpl.SessionFactory;
		//	var implementors = factory.GetImplementors(criteriaImpl.EntityOrClassName);
		//	var loader = new CriteriaLoader((IOuterJoinLoadable)factory.GetEntityPersister(implementors[0]), factory, criteriaImpl, implementors[0], sessionImpl.EnabledFilters);


		//	return loader.SqlString.ToString();
		//}




		public static IEnumerable<TSource> DistinctBy<TSource, TKey>
			(this IEnumerable<TSource> source, Func<TSource, TKey> keySelector)
		{
			HashSet<TKey> seenKeys = new HashSet<TKey>();
			foreach (TSource element in source)
			{
				if (seenKeys.Add(keySelector(element)))
				{
					yield return element;
				}
			}
		}

		public static IMappingExpression<TSource, TDestination> Ignore<TSource, TDestination>(
	   this IMappingExpression<TSource, TDestination> map,
	   Expression<Func<TDestination, object>> selector)
		{
			map.ForMember(selector, config => config.Ignore());
			return map;
		}
		public static PagedResult<T> GetPagedResult<T>(ICriteria criteria, int pageNo, int length, Func<dynamic, T> transformResult, params Order[] order)
		{
			IResultTransformer transformer = null;
			if (criteria is CriteriaImpl)
			{
				transformer = (criteria as CriteriaImpl).ResultTransformer;
			}
			else
			{
				transformer = new RootEntityResultTransformer();
			}


			if (pageNo == 0)
			{
				pageNo = 1;
			}

			int start = (pageNo - 1) * length;
			int count = CriteriaTransformer.TransformToRowCount(criteria).UniqueResult<int>();
			if (count == 0)
			{
				var list0 = new PagedResult<T>();
				return list0;
			}

			if (start >= count)
			{
				start = count - 1;
				start -= start % length;
			}

			criteria.SetResultTransformer(transformer);

			foreach (Order o in order)
			{
				criteria.AddOrder(o);
			}

			var pr = new PagedResult<T>();
			pr.Start = start;
			pr.Total = count;
			pr.Page = pageNo;
			pr.PageLength = length;
			var list = criteria.SetFirstResult(start).SetMaxResults(length).List();
			foreach (var r in list)
			{
				pr.List.Add(transformResult(r));
			}

			return pr;
		}




		public static PagedResult<T> GetPagedResult2<T>(NHibernate.ISession session, ICriteria criteria, int pageNo, int length, Func<dynamic, T> transformResult, params Order[] order)
		{
			IResultTransformer transformer = null;
			if (criteria is CriteriaImpl)
			{
				transformer = (criteria as CriteriaImpl).ResultTransformer;
			}
			else
			{
				transformer = new RootEntityResultTransformer();
			}

			if (pageNo == 0)
			{
				pageNo = 1;
			}

			int start = (pageNo - 1) * length;

			if (order != null)
			foreach (Order o in order)
			{
				criteria.AddOrder(o);
			}

			// Get count first
			var countCriteria = CriteriaTransformer.TransformToRowCount(criteria);
			IList counts = countCriteria.List();
			var totalRecords = (int)counts[0];

			// Get data
			criteria.SetResultTransformer(transformer);
			var data = criteria.SetFirstResult(start).SetMaxResults(length).List();

			// Create result list for compatibility
			IList result = new List<object> { counts, data };


			// totalRecords and data are already set above

			int count = totalRecords;
			if (count == 0)
			{
				var list0 = new PagedResult<T>();
				return list0;
			}

			if (start >= count)
			{
				start = count - 1;
				start -= start % length;
			}

			var pr = new PagedResult<T>();
			pr.Start = start;
			pr.Total = count;
			pr.Page = pageNo;
			pr.PageLength = length;
			var list = data;
			foreach (var r in list)
			{
				pr.List.Add(transformResult(r));
			}

			return pr;
		}



		public static string GeneratePassword(int len)
		{
			string res = "";
			Random rnd = new Random();
			while (res.Length < len) res += (new Func<Random, string>((r) =>
			{
				char c = (char)((r.Next(123) * DateTime.Now.Millisecond % 123));
				return (Char.IsLetterOrDigit(c)) ? c.ToString() : "";
			}))(rnd);
			return res;
		}
	}
}
