using CsvHelper;
using MYOB.AccountRight.SDK;
using MYOB.AccountRight.SDK.Contracts;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using lep.user;
using myob = MYOB.AccountRight.SDK.Services;
using MYOB.AccountRight.SDK.Contracts.Version2.GeneralLedger;
using MYOB.AccountRight.SDK.Contracts.Version2.Sale;
using MYOB.AccountRight.SDK.Services.Contact;
using MYOB.AccountRight.SDK.Services.GeneralLedger;
using MYOB.AccountRight.SDK.Services.Inventory;
using MYOB.AccountRight.SDK.Services.Sale;
 
using MYOB.AccountRight.SDK.Contracts.Version2.Contact;
using lep.order;
using MYOB.AccountRight.SDK.Services;
using System.Net;
//using lepcore.se

namespace WestpackCSV
{
	public class Program
	{
		static void Wait()
		{
			System.Threading.Thread.Sleep(150);
		}
		
		[STAThread]
		static void Main(string[] args)
		{
			Console.Write("Westpack bank csv to MYOB Cloud uploader");
			const int INVOICE_BATCH_SIZE = 100;
			const int REFUND_BATCH_SIZE = 10;
			/*
			TODO 
				put the following filter before release
				-- all finished dates >= 2020-06-26 AND where Invoiced in ('F', 'Y')
			 "Con": "Data Source=PC11532; user id=lepcore; password=********$%MpHU91; Initial Catalog=PRD_AU_2020_07_08_12_10;MultipleActiveResultSets=true;Max Pool Size=200;App=LepCore"
			*/
			
			/*LOCAL*/
			//string LEPConnectionString = "Data Source=.; user id=**; password=**; Initial Catalog=PRD_AU";
			/*LIVE*/ string LEPConnectionString = "Data Source=SRV03;user id=**; password=*************; Initial Catalog=PRD_AU";
			/*TEST*/
			//string LEPConnectionString = "Data Source=NEWMAN;user id=**; password=*************; Initial Catalog=PRD_AU_2020_07_08_12_10";
			const string format = "yyyy-MM-dd HH:mm:ss";
	 		//	SqlConnection lepConn;
			//
			NHibernate.ISession LepSession = null;



			ApiConfiguration configuration = null;
			CompanyFile cf = null;
			CompanyFileCredentials creds = null;

			InvoiceService invoiceService = null;
			ServiceInvoiceService serviceInvoiceService = null;
			CustomerService customerService = null;
			ItemService itemsService = null;
			TaxCodeService taxCodeService = null;
			AccountService accountService = null;
			JobService jobService = null;
			EmployeeService employeeService = null;

			TaxCode ntTaxCode = null;
			TaxCodeLink noTax = null;
	
			var path = string.Empty;

			if( args != null && args.Length > 0 && !string.IsNullOrEmpty(args[0]))
			{
				path = args[0];
				Console.WriteLine($"\nUsing {args[0]}");

			}
			else
			{
				path = @"c:\myob\aa.csv";
			}

			IEnumerable<dynamic> bankPaymentRecords = null;


			using (var reader = new StreamReader(path))
			using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
			{
				bankPaymentRecords = csv.GetRecords<dynamic>()
						.Where(_ => _.Status == "Approved" && _.MerchantId == "********" && float.Parse(_.Amount) > 0 && int.TryParse(_.CustomerReferenceNumber, out int xxx))
					.Select(_ => new
					{
						OrderId = int.Parse(_.CustomerReferenceNumber),
						Amount = decimal.Parse(_.Amount),
						ReceiptNumber = _.ReceiptNumber,
						SettlementDate = DateTime.ParseExact(_.SettlementDate, "yyyyMMdd", CultureInfo.InvariantCulture),
						//Tran**ctionDateTime = DateTime.ParseExact(_.Tran**ctionDateTime, "dd/MM/yyyy H:mm", CultureInfo.InvariantCulture) ?? _.Tran**ctionDateTime,
						}
					).ToList();
			}

			//bankPaymentRecords.Dump();



			if (!bankPaymentRecords.Any())
				return;

			Console.WriteLine($"Found {bankPaymentRecords.Count()} rows to process");




			ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11;
			configuration = new ApiConfiguration(MYOBModule.DeveloperKey, MYOBModule.DeveloperSecret, MYOBModule.MYOBConfirmationUrl);
			var oauthService = new OAuthService(configuration);

			var keystore = new OAuthKeyService2();
			OAuthTokens tokens;
			if (keystore.OAuthResponse != null)
			{
				tokens = keystore.OAuthResponse;
			}
			else
			{
				tokens = oauthService.GetTokens(OAuthLogin.GetAuthorizationCode(configuration));
				keystore.OAuthResponse = tokens;
			}
			var cfService = new CompanyFileService(configuration, null, keystore);
			var companyFiles = cfService.GetRange();
			
			cf = companyFiles.First(c => c.Name.Contains("LEP Colour Printers Pty Ltd"));


			var itemInvoiceService = new myob.Sale.ItemInvoiceService(configuration, null, keystore);

			invoiceService = new myob.Sale.InvoiceService(configuration, null, keystore);
			serviceInvoiceService = new myob.Sale.ServiceInvoiceService(configuration, null, keystore);
			customerService = new myob.Contact.CustomerService(configuration, null, keystore);
			itemsService = new myob.Inventory.ItemService(configuration, null, keystore);
			taxCodeService = new myob.GeneralLedger.TaxCodeService(configuration, null, keystore);
			accountService = new myob.GeneralLedger.AccountService(configuration, null, keystore);
			jobService = new myob.GeneralLedger.JobService(configuration, null, keystore);
			employeeService = new myob.Contact.EmployeeService(configuration, null, keystore);

			Wait();
			var account1_1105 = accountService.GetRange(cf, $"$filter=DisplayID eq '1-1105'", creds).Items.FirstOrDefault();

			Wait();
			// get GST code
			ntTaxCode = taxCodeService.GetRange(cf, $"$filter=Code eq 'N-T'", creds).Items.FirstOrDefault();
			noTax = new TaxCodeLink() { UID = ntTaxCode.UID };




			#region Setup LEP DL
			var nhconfig = new NHibernate.Cfg.Configuration();
			nhconfig.SetProperty(NHibernate.Cfg.Environment.Dialect, typeof(NHibernate.Dialect.MsSql2012Dialect).AssemblyQualifiedName);
			nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionDriver, typeof(NHibernate.Driver.SqlClientDriver).AssemblyQualifiedName);
			nhconfig.SetProperty(NHibernate.Cfg.Environment.Isolation, "ReadCommitted");
			nhconfig.SetProperty(NHibernate.Cfg.Environment.UseSecondLevelCache, false.ToString());
			nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, false.ToString());
			nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionProvider, typeof(NHibernate.Connection.DriverConnectionProvider).AssemblyQualifiedName);
			nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionString, LEPConnectionString);
			nhconfig.SetProperty(NHibernate.Cfg.Environment.CurrentSessionContextClass, "www");
			nhconfig.SetProperty(NHibernate.Cfg.Environment.BatchSize, "1000");
			//nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, "true");
			//nhconfig.SetProperty(NHibernate.Cfg.Environment.FormatSql, "true");
			nhconfig.AddAssembly(typeof(ICustomerUser).Assembly);
			var factory = nhconfig.BuildSessionFactory();
			LepSession = factory.OpenSession();
			#endregion



			


			var errorMsg = "";

			foreach (var r in bankPaymentRecords)
			{
				Console.Write($"\nOrder {r.OrderId}, {r.SettlementDate.ToShortDateString()},  Amount: {r.Amount,10:C2},  Receipt {r.ReceiptNumber}      ");

				var paymentInv = new ServiceInvoice();
				Wait();
				try
				{
					var orderId = (int)r.OrderId;
					var o = LepSession.QueryOver<IOrder>().Where(_ => _.Id == orderId).List().FirstOrDefault();

					if (o == null)
					{
						Console.Write($"Order {r.OrderId} cant be found");
						continue;
					}


					var oldInvs = serviceInvoiceService.GetRange(cf, $"$filter=Number eq 'L{r.OrderId}'", creds);
					
					if (oldInvs.Items.Any())
					{
						Console.Write($" L{r.OrderId} already exists in MYOB, Skipping...");
						continue;
						//foreach (var oldInv in oldInvs.Items)
						//{
						//	Console.Write($"Deleting old invoice {oldInv.Number}");
						//	itemInvoiceService.Delete(cf, oldInv.UID, creds);
						//}
					}




					#region Create a ServiceInvoice header for this order

					var amt = (decimal)(-1 * r.Amount);

					paymentInv.Number = $"L{r.OrderId}";
					paymentInv.CustomerPurchaseOrderNumber = "";
					paymentInv.InvoiceDeliveryStatus = DocumentAction.PrintAndEmail;
					paymentInv.InvoiceType = InvoiceLayoutType.Service;
					paymentInv.Subtotal = amt;
					paymentInv.TotalAmount = amt;
					paymentInv.BalanceDueAmount = amt;
					paymentInv.IsTaxInclusive = false;
					paymentInv.Date = r.SettlementDate;
					var uu = Guid.Parse(o.Customer.MyobUid);
					paymentInv.Customer = new CardLink() { UID = uu };

					#endregion

					var lines = new List<ServiceInvoiceLine>();
					var invLine = new ServiceInvoiceLine();
					invLine.Description = $"LEP ONLINE ORDER {r.OrderId}   Receipt # {r.ReceiptNumber}";
					invLine.Total = amt;
					invLine.TaxCode = noTax;
					invLine.Account = new AccountLink() { UID = account1_1105.UID };
					lines.Add(invLine);

					paymentInv.Lines = lines;

					paymentInv.JournalMemo = $"Sale; {o.Customer.Name}";

					var justCreatedInvoice = serviceInvoiceService.InsertEx(cf, paymentInv, creds);

					Console.Write($" Success. ");

				}
				catch (MYOB.AccountRight.SDK.ApiValidationException ex)
				{
					var errrs = ex.Mes**ge;

					if (ex.Errors != null)
					{
						errrs = errrs + String.Join(", ", ex.Errors.Select(_ => _.Mes**ge).ToArray());
					}
					errorMsg = errrs;
					errorMsg = errorMsg.Replace("'", "''");
					Console.Write($" Fail. " + errorMsg);

					continue;
				}
				catch (ApiCommunicationException ex)
				{
					var errrs = ex.Mes**ge;
					if (ex.Errors != null)
					{
						errrs += "\n" +
						String.Join(", ", ex.Errors.Select(_ => _.Mes**ge).ToArray());
					}

					errorMsg = errrs;
					errorMsg = errorMsg.Replace("'", "''");
					Console.Write($" Fail. " + errorMsg);
					continue;
				}

				catch (Exception ex)
				{
					//ex.Dump();

					Console.Write($" Fail. {ex.Mes**ge} \n {ex.StackTrace}");

					errorMsg = ex.Mes**ge.Replace("'", "''");
					Console.Write($" Fail. " + errorMsg);

					continue;
				}



			}


			Console.WriteLine($"\nPress any key to continue...");
			Console.ReadLine();

		}
	}
}
