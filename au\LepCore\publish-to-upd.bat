@echo off
REM ============================================================================
REM LepCore Publish to c:\lepsf\upd
REM ============================================================================
REM This batch file publishes LepCore using the FolderProfile publish profile
REM Target: c:\lepsf\upd
REM Configuration: Release
REM Platform: x64
REM ============================================================================

echo.
echo ============================================================================
echo  LepCore Publish to c:\lepsf\upd
echo ============================================================================
echo.

REM Set the current directory to the LepCore project directory
cd /d "%~dp0"

REM Check if the project file exists
if not exist "LepCore.csproj" (
    echo ERROR: LepCore.csproj not found in current directory
    echo Current directory: %CD%
    echo Please run this batch file from the LepCore project directory
    pause
    exit /b 1
)

echo Current directory: %CD%
echo Project file: LepCore.csproj
echo Publish profile: FolderProfile
echo Target directory: c:\lepsf\upd
echo.

REM Create target directory if it doesn't exist
if not exist "c:\lepsf\upd" (
    echo Creating target directory: c:\lepsf\upd
    mkdir "c:\lepsf\upd"
)

echo Starting publish process...
echo.

REM Run the dotnet publish command with the FolderProfile
dotnet publish LepCore.csproj ^
    --configuration Release ^
    --framework net8.0-windows7.0 ^
    --runtime win-x64 ^
    --self-contained false ^
    --verbosity normal ^
    /p:PublishProfile=FolderProfile ^
    /p:DeleteExistingFiles=true

REM Check if the publish was successful
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ============================================================================
    echo  PUBLISH SUCCESSFUL!
    echo ============================================================================
    echo.
    echo Published to: c:\lepsf\upd
    echo Configuration: Release
    echo Framework: net8.0-windows7.0
    echo Runtime: win-x64
    echo.
    echo Files in target directory:
    dir "c:\lepsf\upd" /b
    echo.
    echo Publish completed successfully at %DATE% %TIME%
    echo.
) else (
    echo.
    echo ============================================================================
    echo  PUBLISH FAILED!
    echo ============================================================================
    echo.
    echo Error code: %ERRORLEVEL%
    echo Please check the output above for error details.
    echo.
    echo Common issues:
    echo - Build errors in the project
    echo - Missing dependencies
    echo - Insufficient permissions to write to c:\lepsf\upd
    echo - Network issues if accessing remote resources
    echo.
)

echo Press any key to exit...
pause >nul
