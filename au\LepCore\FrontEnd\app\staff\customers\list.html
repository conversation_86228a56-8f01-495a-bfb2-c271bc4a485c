<div leppane="Search">
  <form>
    <div class="row form-horizontal ">
      <div class="col-sm-6">
        <div class="form-group form-group-sm">
          <label class="col-sm-3 control-label" for="customer">Customer</label>
          <div class="col-sm-6">
            <input id="customer" type="text" class="form-control input" ng-model="vm.Customer" ng-minlength="3"
              ng-model-options="{debounce:500}" placeholder="Customer name, number, contact name number" />
          </div>
        </div>

        <div class="form-group form-group-sm">
          <label class="col-sm-3 control-label" for="notes">Notes</label>
          <div class="col-sm-6">
            <input id="notes" type="text" class="form-control input" ng-model="vm.Notes" ng-minlength="3"
              ng-model-options="{debounce:500}" placeholder="Search in customer notes" />
          </div>
        </div>

        <div class="form-group form-group-sm">
          <label class="col-sm-3 control-label" for="order-no">Order #</label>
          <div class="col-sm-6">
            <input id="order-no" type="text" class="form-control input" ng-model="vm.OrderNr"
              ng-model-options="{debounce:500}" />
          </div>
        </div>

        <div class="form-group form-group-sm">
          <label class="col-sm-3 control-label" for="Job-num">Job #</label>
          <div class="col-sm-6">
            <input id="Job-num" type="text" class="form-control input" ng-model="vm.JobNr"
              ng-model-options="{debounce:500}" />
          </div>
        </div>

        <div class="form-group form-group-sm">
          <label class="col-sm-3 control-label" for="">Sales consultant</label>
          <div class="col-sm-6">
            <select class="form-control input" ng-model="vm.SalesConsultant"
              ng-options="k.Name  as k.Name for k in SalesConsultant"
              >
              <option value="">-- Select --</option>
            </select>
          </div>
        </div>


        <div class="form-group form-group-sm">
          <label class="col-sm-3 control-label" for="CustomerStatus">Customer Status</label>
          <div class="col-sm-6">
            <select id="CustomerStatus" class="form-control input" ng-model="vm.CustomerStatus"
              ng-options="k  as k  for k in CustomerStatus" >
              <option value="">-- Select --</option>
            </select>
          </div>
        </div>


        <div class="form-group form-group-sm">
          <label class="col-sm-3 control-label" for="BusinessType">Business Type</label>
          <div class="col-sm-6">
            <select id="BusinessType" class="form-control input" ng-model="vm.BusinessType"
              ng-options="k.Id  as k.Name  for k in BusinessTypes" >
              <option value="">-- Select --</option>
            </select>
          </div>
        </div>

      </div>

      <div class="col-sm-6">


        <div class="form-group form-group-sm">
          <label class="col-sm-4 control-label" for="payment-terms">Payment terms</label>
          <div class="col-sm-6">
            <select id="payment-terms" name="templateId" class="form-control input" ng-model="vm.PaymentTerms">
              <option selected="selected" value="">-- Any --</option>
              <option value="Account">On Account</option>
              <option value="PrePay">Pre-pay</option>
              <option value="COD">Cash Before Dispatch</option>
              <option value="OnHold">On Hold</option>
            </select>
            <!--

              <label class="control-label"><input type="radio" name="PaymmentTerms" ng-model="vm.PaymentTerms" ng-value=""  /> Any</label>&nbsp;&nbsp;
              <label class="control-label"><input type="radio" name="PaymmentTerms" ng-model="vm.PaymentTerms" ng-value="'Account'"  /> On Account</label>&nbsp;&nbsp;
              <label class="control-label"><input type="radio" name="PaymmentTerms" ng-model="vm.PaymentTerms" ng-value="'PrePay'" /> Pre-Pay</label>&nbsp;&nbsp;
              <label class="control-label"><input type="radio" name="PaymmentTerms" ng-model="vm.PaymentTerms" ng-value="'COD'" /> COD </label>&nbsp;&nbsp;
              <label class="control-label"><input type="radio" name="PaymmentTerms" ng-model="vm.PaymentTerms" ng-value="'OnHold'" /> On Hold </label>&nbsp;&nbsp;
            -->

          </div>
        </div>

        <div class="form-group form-group-sm">
          <label class="col-sm-4 control-label" for="payment-terms">Has system access</label>
          <div class="col-sm-6">
            <label class="control-label"><input type="radio" name="SystemAccess" ng-model="vm.SystemAccess"
                ng-value="true" /> Yes </label>&nbsp;&nbsp;
            <label class="control-label"><input type="radio" name="SystemAccess" ng-model="vm.SystemAccess"
                ng-value="false" /> No </label>&nbsp;&nbsp;
            <label class="control-label"><input type="radio" name="SystemAccess" ng-model="vm.SystemAccess"
                ng-value="" /> Either </label>&nbsp;&nbsp;
          </div>
        </div>




        <div class="form-group form-group-sm">
          <label class="col-sm-4 control-label" for="payment-terms"> </label>
          <div class="col-sm-6">
            <label class="control-label">
              <input type="checkbox" ng-model="vm.IsPrintPortalEnabled" />
              Print Portal enabled
            </label>
          </div>
        </div>

        <div class="form-group form-group-sm">
          <label class="col-sm-4 control-label" for="RegionLep">LEP Region</label>
          <div class="col-sm-6">
            <select id="RegionLep" class="form-control input" ng-model="vm.RegionLep"
              ng-options="k  as k for k in RegionLep" >
              <option value="">-- Select --</option>
            </select>
          </div>
        </div>

        <div class="form-group form-group-sm">
          <label class="col-sm-4 control-label" for="PostalPostCode">Postal Post Code</label>
          <div class="col-sm-6">
            <label class="control-label">
              <input type="text" id="PostalPostCode" ng-model="vm.PostalPostCode" class="form-control" maxlength="4" />

            </label>
          </div>
        </div>

        <div class="form-group form-group-sm">
          <label class="col-sm-4 control-label" for="FranchiseCode">Franchise Code</label>
          <div class="col-sm-6">
            <select id="FranchiseCode" class="form-control input" ng-model="vm.FranchiseCode"
              ng-options="k.code  as k.name for k in Franchise"
              >
              <option value="">-- Select --</option>
            </select>
          </div>
        </div>

        <div class="form-group form-group-sm">
          <label class="col-sm-4 control-label" for="UnableToMeetPrice"> </label>
          <div class="col-sm-6">
            <label class="control-label">
              <input type="checkbox" ng-model="vm.UnableToMeetPrice" id="UnableToMeetPrice" />
              Unable to meet price
            </label>
          </div>
        </div>


      </div>

      <div class="col-sm-12">
        <div class="pull-right">
          <button type="reset" class="btn " ng-click="clear()"><i class="glyphicon glyphicon-erase"></i> Clear</button>
          <button id="cust-btn" class="btn btn-default" ng-click="search()"><i class="glyphicon glyphicon-search"></i>
            Search</button>
        </div>
      </div>
    </div>
  </form>
</div>


<div class="row">
  <div class="col-sm-12">
    <div class="actions">
      <a class="" ui-sref="staff.customers-view({id: 0})">

        <i class="glyphicon glyphicon-plus"></i>
        Add new Customer
      </a>
      <span class="btn" ng-click="OnHoldExport()"><i class="glyphicon glyphicon-cloud-download"></i>Download On Hold
        Customers</span>
      <span class="btn" ng-click="OnHoldImport()"> <i class="glyphicon  glyphicon-cloud-upload"></i>Upload On Hold
        Customers</span>



      <a class="btn" ng-click="searchDownload()"><i class="glyphicon glyphicon-cloud-download"></i> Download Customers
      </a>
    </div>
  </div>

</div>
<br />

<div leppane="Customers">
  <div class="row">
    <div class="col-sm-12 white">

      <table class="table table-striped table-hover">
        <thead>
          <tr>
            <th><a ng-click="toggleSort('Id')">Cust #</a></th>
            <th><a ng-click="toggleSort('PaymentTerms')">Terms</a></th>
            <th><a ng-click="toggleSort('Name')">Customer</a></th>
            <th><a ng-click="toggleSort('Contact1.Name')">Contact</a></th>
            <th><a ng-click="toggleSort('Contact1.Phone')">Phone</a></th>
            <th><a ng-click="toggleSort('LastOrderDate')">Last Order</a></th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>

          <tr ng-animate="'animate'" ng-repeat="o in r.List">
            <td>{{::o.CustomerNr}}</td>
            <td>{{::$root.enums.ValueKey.PaymentTermsOptions[o.PaymentTerms]}}</td>
            <td>
              <a ui-sref="staff.customers-view({id: o.Id})">{{o.Name}}</a>

            </td>
            <td>{{::o.Contact1Name}}</td>
            <td>{{::o.Contact1Phone}}</td>
            <td>{{::o.LastOrderDate | date:'dd-MMM-yyyy'}}, <span am-time-ago="::o.LastOrderDate  "></span></td>
            <td>

              <a ng-click="newOrder(o.Id)">New order</a>
            </td>
          </tr>
        </tbody>
      </table>

      {{r.Summary}}
      <div paging page="r.Page" page-size="r.PageLength" total="r.Total" paging-action="goPage(page)" scroll-top="false"
        hide-if-empty="true" show-prev-next="true" show-first-last="true"
        text-next-class="glyphicon glyphicon-chevron-right" text-prev-class="glyphicon glyphicon-chevron-left"
        text-first-class="glyphicon glyphicon-backward" text-last-class="glyphicon glyphicon-forward">
      </div>
    </div>
  </div>
</div>