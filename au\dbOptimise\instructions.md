Objective:
"Your primary goal is to analyze the performance of the specified SQL Server database(s) and identify opportunities to optimize table structures, indexing strategies, and query execution plans. Your output should be actionable T-SQL scripts that can be executed via sqlcmd or similar programmatic methods, along with clear justifications and a rollback plan."

Scope of Analysis:
"Focus your analysis on the following key areas for the specified database(s) on [Your SQL Server Instance Name/IP] (e.g., SERVER\SQLEXPRESS or *************):

Index Optimization:

Identify missing indexes that would significantly improve query performance.
Detect redundant or overlapping indexes.
Recommend modifications to existing indexes (e.g., adding included columns, changing clustering).
Suggest appropriate index maintenance (rebuild/reorganize) based on fragmentation levels and usage patterns.
Specific considerations: Analyze sys.dm_db_missing_index_details, sys.dm_db_index_usage_stats, and fragmentation levels.
Query Plan Analysis:

Identify the top N (e.g., 20-50, configurable) most expensive queries based on CPU, I/O, and duration.
Analyze their execution plans to pinpoint bottlenecks (e.g., table scans, inefficient joins, excessive sorts, key lookups).
Suggest query rewrites or hints where appropriate to improve execution plans.
Specific considerations: Utilize sys.dm_exec_query_stats, Query Store data (if enabled), and sys.dm_exec_text_query_plan for plan analysis.
Table Structure Review (Limited Scope):

Flag tables with excessive column counts that might impact performance.
Identify tables with data types that are unnecessarily large or inefficient for the stored data. (e.g., NVARCHAR(MAX) when NVARCHAR(50) would suffice).
Note: Structural changes to tables should be explicitly flagged as high-impact and require additional human review.
Statistics Management:

Identify outdated or missing statistics that could lead to poor cardinality estimates and inefficient query plans.
Recommend T-SQL to update statistics (e.g., UPDATE STATISTICS WITH FULLSCAN or WITH RESAMPLE).
Output Requirements:

"For each identified optimization opportunity, generate the following:

Justification: A brief explanation of why the optimization is recommended, referencing relevant metrics (e.g., query execution time improvement, reduction in logical reads, fragmentation percentage, missing index impact estimate).
T-SQL Script for Implementation:
For index changes: CREATE INDEX, ALTER INDEX, DROP INDEX statements.
For statistics: UPDATE STATISTICS statements.
For query rewrites: The proposed rewritten query and the original query for comparison.
All scripts must be idempotent where possible (i.e., running them multiple times doesn't cause errors or unintended side effects beyond the first execution).
Scripts should be formatted for readability and include comments.
Rollback Plan (T-SQL): A corresponding T-SQL script that can revert the proposed change, if applicable (e.g., DROP INDEX for a CREATE INDEX, or the original query for a rewrite).
Impact Assessment: An estimated impact level (Low, Medium, High) and potential performance gain for each recommendation.
Execution Instructions: Clear instructions on how to execute the generated T-SQL scripts using sqlcmd, including example commands.
Assumptions & Access:

"You have read-only access to the necessary SQL Server DMVs, Query Store, and system views.
"You are operating within a safe, non-production environment initially for testing recommendations.
"Any DDL (Data Definition Language) operations (e.g., CREATE INDEX, ALTER TABLE) will be explicitly approved by a human DBA before production deployment."
Example Prompt Addition (if you have specific databases/queries in mind):

"Initial Focus Area (Optional but Recommended):
"Begin your analysis by examining the database [SRV03].[PRD_AU] and specifically look for performance bottlenecks related to queries involving tables: [Order], [Job], and [Runs]. Also, analyze the top 5 most frequent queries from the last 24 hours within [PRD_AU]."

Connection string: "Data Source=srv03; user id=sa; password=*************; Initial Catalog=PRD_AU;MultipleActiveResultSets=true;Max Pool Size=200;App=LepCore"

Create SQL SCripts in C:\LepSF\au\dbOptimise