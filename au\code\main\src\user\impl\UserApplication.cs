using lep.configuration;
using lep.order;
using lep.security;
using lumen.csv;
using NHibernate;
using NHibernate.Criterion;
using NHibernate.Transform;
using NHibernate.Type;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using lep.job;
using lep.user;

namespace lep.user.impl
{
	public class UserApplication : BaseApplication, IUserApplication
	{
		private IConfigurationApplication _configApp;
		private BcryptPasswordAlgorithm passwordAlgorithm;

		public UserApplication(ISession sf, ISecurityApplication _securityApp, IConfigurationApplication configApp) : base(sf, _securityApp)
		{
			_configApp = configApp;
			passwordAlgorithm = new BcryptPasswordAlgorithm();
			passwordAlgorithm.LogRounds = 10;
		}


		public ICustomerUser NewCustomerUser()
		{
			//AssertPermission("user.create");
			var customer = new CustomerUser();
			customer.PaymentTerms = PaymentTermsOptions.Account;
			customer.HasSystemAccess = true;
			var creditLimit = Convert.ToInt32(_configApp.GetValue(Configuration.OrderLimit));
			customer.CreditLimit = creditLimit;
			customer.IsChargedGST = true;

			customer.MYOB = "-";
			customer.ABN = "";
			customer.AllowedSamples = true;
			customer.DateModified = customer.DateCreated = DateTime.Now;
			return customer;
		}

		public IStaff NewStaff()
		{
			//AssertPermission("staff.create");
			return new Staff() {  DateCreated = DateTime.Now, DateModified = DateTime.Now};
		}

		public IUser GetUser(int Id)
		{
			return Get<IUser>(Id);
		}

		public IUser Refresh(IUser user)
		{
			var id = user.Id;
			Session.Evict(user);
			return Session.Get<IUser>(id);
		}

		public IStaff GetSystemUser()
		{
			return Get<IUser>(1) as IStaff;
		}

		public void Save(IUser user)
		{
			//AssertPermission( "user.update" );
			base.Save<IUser>(user);
		}

		public void SetPassword(IUser user, string password)
		{
			user.HashPassword = HashPassword(password);
		}

		public ICustomerUser GetCustomerUser(string username)
		{
			return Session.CreateCriteria(typeof(ICustomerUser))
				.Add(Restrictions.Eq("Username", username))
				.UniqueResult() as ICustomerUser;
		}

		public ICustomerUser GetCustomerUser(int customerId)
		{
			return Session.CreateCriteria(typeof(ICustomerUser))
				.Add(Restrictions.Eq("Id", customerId))
				.UniqueResult() as ICustomerUser;
		}

		private List<string> ext = new List<string> { ".jpg", ".png" };

		//  returns cusomers logo matched by his username if the logo exists
		//  png logo files are served before jpg logo files
		public FileInfo GetCustomerLogo(string customerUsername)
		{
			FileInfo file = null;

			var files = LepGlobal.Instance.CustomerLogoDirectory.GetFiles(customerUsername + "*", SearchOption.TopDirectoryOnly)
				.Where(s => ext.Contains(Path.GetExtension(s.FullName).ToLower())).ToList();

			if (files.Count() > 0)
			{
				if ((file = files.FirstOrDefault(f => Path.GetExtension(f.FullName).ToLower() == ".png")) == null)
					if ((file = files.FirstOrDefault(f => Path.GetExtension(f.FullName).ToLower() == ".jpg")) == null) { };
			}

			return file;
		}

		public void SaveCustomerLogo(string customerUsername, Stream file, string extension)
		{
			var oldLogoFiles = LepGlobal.Instance.CustomerLogoDirectory.GetFiles(customerUsername + "*", SearchOption.TopDirectoryOnly)
					.Where(s => ext.Contains(Path.GetExtension(s.FullName).ToLower())).ToList();

			foreach (var oldLogo in oldLogoFiles)
			{
				oldLogo.Delete();
			}

			var path = Path.Combine(LepGlobal.Instance.CustomerLogoDirectory.FullName, customerUsername + extension);
			using (var fileStream = new FileStream(path, FileMode.Create))
			{
				file.CopyTo(fileStream);
			}
		}

		public IUser AttemptLogin(string username, string password)
		{
			// OPTIMIZATION: Use simple criteria without eager loading to avoid complex JOINs
			// This prevents the massive SQL query that was causing errors
			var u = Session.CreateCriteria(typeof(IUser))
				.Add(Restrictions.Eq("Username", username))
				.Add(Restrictions.Eq("IsEnabled", true))
				.AddOrder(Order.Asc("Id"))
				.SetMaxResults(1)
				.UniqueResult() as IUser;

			if (u != null && CheckPassword(password, u.HashPassword) &&
				(!(u is ICustomerUser customerUser) || customerUser.SiteLocation == SiteLocation))
			{
				return u;
			}
			return null;
		}

		public ICriteria StaffCriteria(string username, string firstname, string lastname, List<Role> roles)
		{
			var criteria = Session.CreateCriteria(typeof(IStaff));

			if (!String.IsNullOrEmpty(username))
			{
				criteria.Add(Restrictions.Like("Username", username, MatchMode.Anywhere));
			}
			if (!String.IsNullOrEmpty(firstname))
			{
				criteria.Add(Restrictions.Like("FirstName", firstname, MatchMode.Anywhere));
			}
			if (!String.IsNullOrEmpty(lastname))
			{
				criteria.Add(Restrictions.Like("LastName", lastname, MatchMode.Anywhere));
			}
			if (roles.Count > 0)
			{
				criteria.Add(Restrictions.In("Role", roles));
			}

			criteria.Add(Restrictions.Not(Restrictions.Eq("Role", Role.System)));

			return criteria;
		}

		public ICriteria CustomerCriteria(string customer, int customerid, int orderid, int jobid, bool? systemAccess,
			PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false, bool showArchived = false)
		{
			var criteria = Session.CreateCriteria(typeof(ICustomerUser), "cust");
				//.CreateAlias("cust.PostalAddress", "pa") ;

			var conjunction = new Conjunction();
			var d = new Disjunction();


			if (!String.IsNullOrEmpty(customer))
			{
				d.Add(Restrictions.Like("cust.Name", customer, MatchMode.Anywhere));
				//d.Add(Restrictions.Like("cust.Contact1.Name", customer, MatchMode.Anywhere))
				//d.Add(Restrictions.Like("cust.Contact1.Phone", customer, MatchMode.Anywhere))
				//d.Add(Restrictions.Like("cust.Contact1.Mobile", customer, MatchMode.Anywhere))
				//d.Add(Restrictions.Like("cust.Contact2.Name", customer, MatchMode.Anywhere))
				//d.Add(Restrictions.Like("cust.Contact2.Phone", customer, MatchMode.Anywhere))
				//d.Add(Restrictions.Like("cust.Contact2.Mobile", customer, MatchMode.Anywhere))
				d.Add(Restrictions.Like("cust.ContactsJsonStr", customer, MatchMode.Anywhere));
				d.Add(Restrictions.Like("cust.Username", customer, MatchMode.Anywhere));

				if (customerid != 0)
				{
					d.Add(Restrictions.Eq("cust.Id", customerid));
				}
				criteria.Add(d);
			}


			if (systemAccess.HasValue)
			{
				criteria.Add(Restrictions.Eq("cust.HasSystemAccess", systemAccess));
			}

			if (paymentTermsOptions.HasValue)
			{
				criteria.Add(Restrictions.Eq("cust.PaymentTerms", paymentTermsOptions));
			}

			if (IsPrintPortalEnabled)
			{
				criteria.Add(Restrictions.Eq("cust.IsPrintPortalEnabled", true));
			}

			if (orderid != 0 || jobid != 0)
			{
				var ordercriteria = DetachedCriteria.For(typeof(IOrder), "o");

				if (orderid != 0)
				{
					ordercriteria.Add(Restrictions.Eq("Id", orderid));
				}
				if (jobid != 0)
				{
					ordercriteria.CreateAlias("Jobs", "job");
					ordercriteria.Add(Restrictions.Eq("job.Id", jobid));
				}
				ordercriteria.SetProjection(Projections.Alias(Projections.Property("Customer"), "cust1"));
				ordercriteria.Add(Restrictions.EqProperty("Customer.Id", "cust.Id"));
				criteria.Add(Subqueries.Exists(ordercriteria));
			}

			criteria.SetProjection(Projections.ProjectionList()
					.Add(Projections.Property("cust.Id"), "Id")
					.Add(Projections.Property("cust.PaymentTerms"), "PaymentTerms")
					.Add(Projections.Property("cust.Name"), "Name")
					.Add(Projections.Property("cust.LastOrderDate"), "LastOrderDate")
					.Add(Projections.Property("cust.Contacts"), "Contacts")
					.Add(Projections.Property("cust.Archived"), "Archived")
					//.Add(Projections.Property("cust.ParentCustomer"), "ParentCustomer")
					)
				.SetResultTransformer(new AliasToBeanResultTransformer(typeof(CustomerUser)));

			return criteria;
		}



		public ICriteria CustomerCriteria2(string customer, int customerid, int orderid, int jobid, bool? systemAccess,
			PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false, bool showArchived = false,
			string PostalPostCode = null, string SalesConsultant = null, string CustomerStatus = null,
			string RegionLep = null, string FranchiseCode = null, string BusinessType=null, string notes=null)
		{
			var criteria = Session.CreateCriteria(typeof(ICustomerUser), "cust");

			// OPTIMIZATION 1: Early exit for specific ID searches
			if (customerid != 0)
			{
				criteria.Add(Restrictions.Eq("cust.Id", customerid));
				return criteria.SetCacheable(true);
			}

			// OPTIMIZATION 2: Most selective filters first (database optimization principle)

			// Apply exact match filters first (most selective)
			if (!string.IsNullOrEmpty(SalesConsultant))
			{
				criteria.Add(Restrictions.Eq("cust.SalesConsultant", SalesConsultant));
			}

			if (!string.IsNullOrEmpty(CustomerStatus))
			{
				criteria.Add(Restrictions.Eq("cust.CustomerStatus", CustomerStatus));
			}

			if (!string.IsNullOrEmpty(FranchiseCode))
			{
				criteria.Add(Restrictions.Eq("cust.FranchiseCode", FranchiseCode));
			}

			if (!string.IsNullOrEmpty(BusinessType))
			{
				criteria.Add(Restrictions.Eq("cust.BusinessType", BusinessType));
			}

			if (systemAccess.HasValue)
			{
				criteria.Add(Restrictions.Eq("cust.IsEnabled", systemAccess));
			}

			if (paymentTermsOptions.HasValue)
			{
				criteria.Add(Restrictions.Eq("cust.PaymentTerms", paymentTermsOptions));
			}

			if (IsPrintPortalEnabled)
			{
				criteria.Add(Restrictions.Eq("cust.IsPrintPortalEnabled", true));
			}

			// OPTIMIZATION 3: Improved postcode filtering (prefix match is much faster)
			if (!string.IsNullOrEmpty(PostalPostCode))
			{
				criteria.Add(Restrictions.Like("cust.BillingAddress.Postcode", PostalPostCode, MatchMode.Start));
			}

			// OPTIMIZATION 4: Optimized customer name/username search
			if (!String.IsNullOrEmpty(customer))
			{
				var customerDisjunction = new Disjunction();

				// Use prefix matching instead of anywhere for better performance
				customerDisjunction.Add(Restrictions.Like("cust.Name", customer, MatchMode.Start));
				customerDisjunction.Add(Restrictions.Like("cust.Username", customer, MatchMode.Start));

				// Only use expensive ANYWHERE searches if the string is long enough to be meaningful
				if (customer.Length >= 3)
				{
					customerDisjunction.Add(Restrictions.Like("cust.Name", customer, MatchMode.Anywhere));
					customerDisjunction.Add(Restrictions.Like("cust.Username", customer, MatchMode.Anywhere));

					// ContactsJsonStr search only for longer strings (very expensive)
					if (customer.Length >= 4)
					{
						customerDisjunction.Add(Restrictions.Like("cust.ContactsJsonStr", customer, MatchMode.Anywhere));
					}
				}

				criteria.Add(customerDisjunction);
			}

			// OPTIMIZATION 5: Improved Order/Job subquery with better performance
			if (orderid != 0 || jobid != 0)
			{
				var orderCriteria = DetachedCriteria.For(typeof(IOrder), "o");

				if (orderid != 0)
				{
					orderCriteria.Add(Restrictions.Eq("o.Id", orderid));
				}

				if (jobid != 0)
				{
					// Use EXISTS subquery instead of JOIN for better performance
					var jobSubquery = DetachedCriteria.For(typeof(IJob), "j")
						.Add(Restrictions.Eq("j.Id", jobid))
						.Add(Restrictions.EqProperty("j.Order.Id", "o.Id"))
						.SetProjection(Projections.Constant(1));

					orderCriteria.Add(Subqueries.Exists(jobSubquery));
				}

				orderCriteria.SetProjection(Projections.Property("o.Customer.Id"));
				orderCriteria.Add(Restrictions.EqProperty("o.Customer.Id", "cust.Id"));

				criteria.Add(Subqueries.Exists(orderCriteria));
			}

			// OPTIMIZATION 6: Fixed SQL injection vulnerability and improved RegionLep filter
			if (!string.IsNullOrEmpty(RegionLep))
			{
				// SECURITY FIX: Use parameterized SQL instead of string concatenation
				var regionSql = @"EXISTS (
					SELECT 1 FROM SalesRegion sr
					WHERE sr.PostCode = {alias}.BillingPostcode
					AND sr.LEP_Region = ?)";

				criteria.Add(Expression.Sql(regionSql, RegionLep, NHibernateUtil.String));
			}

			// OPTIMIZATION 7: Advanced notes search with multiple strategies
			if (!string.IsNullOrEmpty(notes))
			{
				// Only search notes if the search term is meaningful (3+ characters)
				if (notes.Length >= 3)
				{
					// Strategy 1: Try full-text search first (if available and supported)
					if (notes.Length >= 4 && !notes.Contains("%") && !notes.Contains("_"))
					{
						try
						{
							// Use full-text search for better performance on longer queries
							var fullTextSql = @"EXISTS (
								SELECT 1 FROM [PRD_AU_Notes].[dbo].[CustomerNotes1] fn
								WHERE fn.CustomerId = {alias}.Id
								AND CONTAINS(fn.NoteText, ?))";

							criteria.Add(Expression.Sql(fullTextSql, notes, NHibernateUtil.String));
						}
						catch
						{
							// Fall back to traditional search if full-text is not available
							var notesCriteria = DetachedCriteria.For(typeof(CustomerNote), "note")
								.Add(Restrictions.Like("note.NoteText", notes, MatchMode.Anywhere))
								.SetProjection(Projections.Property("note.Customer.Id"))
								.Add(Restrictions.EqProperty("note.Customer.Id", "cust.Id"));

							criteria.Add(Subqueries.Exists(notesCriteria));
						}
					}
					else
					{
						// Strategy 2: Optimized traditional search for shorter terms or special characters
						var notesCriteria = DetachedCriteria.For(typeof(CustomerNote), "note")
							.Add(Restrictions.Like("note.NoteText", notes, MatchMode.Anywhere))
							.SetProjection(Projections.Property("note.Customer.Id"))
							.Add(Restrictions.EqProperty("note.Customer.Id", "cust.Id"));

						criteria.Add(Subqueries.Exists(notesCriteria));
					}
				}
			}

			// OPTIMIZATION 8: Enable query caching for reference data
			criteria.SetCacheable(true);

			return criteria;
		}

		// OPTIMIZATION 9: Add a projection-optimized version for list views
		public ICriteria CustomerCriteria2WithProjection(string customer, int customerid, int orderid, int jobid,
			bool? systemAccess, PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false,
			bool showArchived = false, string PostalPostCode = null, string SalesConsultant = null,
			string CustomerStatus = null, string RegionLep = null, string FranchiseCode = null,
			string BusinessType = null, string notes = null)
		{
			var criteria = CustomerCriteria2(customer, customerid, orderid, jobid, systemAccess,
				paymentTermsOptions, IsPrintPortalEnabled, showArchived, PostalPostCode,
				SalesConsultant, CustomerStatus, RegionLep, FranchiseCode, BusinessType, notes);

			// Use projection for list views to reduce data transfer and improve performance
			criteria.SetProjection(Projections.ProjectionList()
				.Add(Projections.Property("cust.Id"), "Id")
				.Add(Projections.Property("cust.Name"), "Name")
				.Add(Projections.Property("cust.CustomerNr"), "CustomerNr")
				.Add(Projections.Property("cust.PaymentTerms"), "PaymentTerms")
				.Add(Projections.Property("cust.LastOrderDate"), "LastOrderDate")
				.Add(Projections.Property("cust.Contact1.Name"), "Contact1Name")
				.Add(Projections.Property("cust.Contact1.Phone"), "Contact1Phone"))
				.SetResultTransformer(new AliasToBeanResultTransformer(typeof(CustomerUser)));

			return criteria;
		}

		// OPTIMIZATION 10: Count method for pagination support
		public int CustomerCriteria2Count(string customer, int customerid, int orderid, int jobid,
			bool? systemAccess, PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false,
			bool showArchived = false, string PostalPostCode = null, string SalesConsultant = null,
			string CustomerStatus = null, string RegionLep = null, string FranchiseCode = null,
			string BusinessType = null, string notes = null)
		{
			var criteria = CustomerCriteria2(customer, customerid, orderid, jobid, systemAccess,
				paymentTermsOptions, IsPrintPortalEnabled, showArchived, PostalPostCode,
				SalesConsultant, CustomerStatus, RegionLep, FranchiseCode, BusinessType, notes);

			criteria.SetProjection(Projections.RowCount());
			return criteria.UniqueResult<int>();
		}


		public IStaff GetStaffByIP(string ip)
		{
			var staff = Session.CreateCriteria(typeof(IStaff))
				.Add(Restrictions.Eq("IPAddress", ip))
				.AddOrder(Order.Desc("LastLogin"))
				.SetMaxResults(1)
				.UniqueResult() as IStaff;

			if (staff == null)
			{
				staff = Staff.SYSTEM;
			}
			return staff;
		}

		public bool CheckBusinessName(ICustomerUser customer)
		{
			var criteria = Session.CreateCriteria(typeof(ICustomerUser));
			criteria.Add(Restrictions.Eq("Name", customer.Name));
			criteria.Add(Restrictions.Not(Restrictions.Eq("Id", customer.Id)));
			criteria.SetProjection(Projections.RowCount());
			return (int)criteria.List()[0] == 0;
		}

		public bool IsValidUsername(string username, IUser user)
		{
			var criteria = Session.CreateCriteria(typeof(IUser))
				.Add(Restrictions.Eq("Username", username))
				.Add(Restrictions.Not(Restrictions.Eq("Id", user.Id)))
				.SetProjection(Projections.RowCount());
			return (int)criteria.List()[0] == 0;
		}

		public bool IsUniqueMYOB(string myob, ICustomerUser customer)
		{
			if (myob == "") return true;
			var count = (int)Session.CreateCriteria(typeof(ICustomerUser))
				.Add(Restrictions.Eq("MYOB", myob))
				.Add(Restrictions.Not(Restrictions.Eq("Id", customer.Id)))
				.SetProjection(Projections.RowCount()).UniqueResult();

			return count == 0;
		}

		public IList<ICustomerUser> FindOnHoldCustomer()
		{
			return Session.CreateCriteria(typeof(ICustomerUser))
				.Add(Restrictions.Eq("PaymentTerms", PaymentTermsOptions.OnHold))
				.AddOrder(Order.Asc("Name"))
				.List<ICustomerUser>();
		}

		public void ExportOnHold(CsvSerialiser serial)
		{
			serial.AlwaysQuote = true;
			serial.RowData(new string[]
			{
				"Business Name", "Primary Contact Name", "Primary Contact Phone", "Primary Contact Mobile",
				"Primary Contact Fax", "Web-Customer#", "MYOB-Customer#"
			});

			foreach (var c in FindOnHoldCustomer())
			{
				serial.RowData(new string[]
				{
					c.Name, c.Contact1.Name, c.Contact1.AreaCode + " " + c.Contact1.Phone, c.Contact1.Mobile,
					c.Contact1.FaxAreaCode + " " + c.Contact1.Fax, c.CustomerNr, c.MYOB
				});
			}
		}

		public void UpdateOnHold(List<string> customers)
		{
			var criteria = Session.CreateCriteria(typeof(ICustomerUser))
				.Add(Restrictions.In("MYOB", customers.ToArray()));
			foreach (var c in criteria.List<ICustomerUser>())
			{
				c.PaymentTerms = PaymentTermsOptions.OnHold;
				Save<ICustomerUser>(c);
			}
		}

		public Decimal GetCustomerLEPOnlineBalance(int UserID)
		{
			var sql = @"Select dbo.GetCustomerLEPOnlineBalance(:UserID)";
			var query = Session.CreateSQLQuery(sql).SetInt32("UserID", UserID);
			return query.UniqueResult<Decimal>();
		}

		public Decimal GetCustomerAvailableBalance(int UserID)
		{
			var sql = @"Select dbo.GetCustomerAvailableBalance(:UserID)";
			var query = Session.CreateSQLQuery(sql).SetInt32("UserID", UserID);
			return query.UniqueResult<Decimal>();
		}

		public bool NeedMergeCustomer(ICustomerUser customer)
		{
			var criteria = Session.CreateCriteria(typeof(IOrder))
				.Add(Restrictions.Eq("Customer", customer))
				.SetProjection(Projections.RowCount());
			return (int)criteria.List()[0] != 0;
		}

		public void MergeCustomer(ICustomerUser from, ICustomerUser to)
		{
			var cmd = Session.Connection.CreateCommand();
			cmd.CommandText = "MergeCustomer";
			cmd.CommandType = CommandType.StoredProcedure;
			var fromP = new SqlParameter("@fromid", SqlDbType.Int, 4);
			fromP.Value = from.Id;
			var toP = new SqlParameter("@toid", SqlDbType.Int, 4);
			toP.Value = to.Id;
			cmd.Parameters.Add(fromP);
			cmd.Parameters.Add(toP);
			cmd.ExecuteNonQuery();

			Session.Refresh(to);
			Session.Refresh(from);
		}

		public void Delete(ICustomerUser customer)
		{
			base.Delete<ICustomerUser>(customer);
		}

		public void Delete(IUser user)
		{
			base.Delete<IUser>(user);
		}

		public IList<IUser> GetUserByName(string username)
		{
			return Session.CreateCriteria(typeof(IUser))
				.Add(Restrictions.Eq("Username", username))
				.List<IUser>();
		}

		private string HashPassword(string password)
		{
			SHA1 sha = SHA1.Create();

			return passwordAlgorithm.HashPassword(Convert.ToBase64String(sha.ComputeHash(
				new ASCIIEncoding().GetBytes(password))));
		}

		private bool CheckPassword(string password, string hashpassword)
		{
			//return true;

			SHA1 sha = SHA1.Create();
			password = Convert.ToBase64String(sha.ComputeHash(
				new ASCIIEncoding().GetBytes(password)));

			return passwordAlgorithm.CheckPassword(password, hashpassword);
		}

		public IList GetCustomerProductPricing()
		{
			var sql = "SELECT [Code],[Description]  FROM [CustomerProductPricing]";
			var result = Session.CreateSQLQuery(sql).List();
			return result;
		}

		public IList GetCustomerFreightPricing()
		{
			var sql = "SELECT [Code] ,[Description] FROM [dbo].[CustomerFreightPricing]";
			var result = Session.CreateSQLQuery(sql).List();
			return result;
		}

		public IList<ICustomerUser> GetSubCustomers(ICustomerUser parent)
		{
			return Session.CreateCriteria(typeof(ICustomerUser))
				.Add(Restrictions.Eq("ParentCustomer", parent))
				.AddOrder(Order.Asc("Name"))
				.List<ICustomerUser>();

			//var sortOrder = new[] { order };

			//var list = Utils.GetPagedResult2(session, criteria, sp.Page, 20, x => new CustomerListDTO(x), sortOrder);

			//return new OkObjectResult(list);
		}
	}
}
