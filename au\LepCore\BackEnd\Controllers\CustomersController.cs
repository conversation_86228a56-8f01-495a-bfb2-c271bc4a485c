using AutoMapper;
using CsvHelper;
using lep;
using lep.email;
using lep.order;
using lep.promotion;
using lep.user;
using lep.user.impl;
using LepCore.Dto;
using lumen.csv;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using NHibernate.Criterion;
using NHibernate.Transform;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;

namespace LepCore.Controllers
{
	/// <summary>
	/// </summary>
	[Produces("application/json")]
	[Route("api/Staff/[controller]")]
	[Authorize(Roles = LepRoles.Staff)]
	[ApiExplorerSettings(IgnoreApi = true)]
	public class CustomersController : Controller
	{
		private readonly IMapper _mapper;
		private readonly IUserApplication _userApplication;
		private readonly IEmailApplication _emailApplication;
		private readonly IOrderApplication _orderApp;
		private NHibernate.ISession _session;
		private readonly IHttpContextAccessor _contextAccessor;
		private CachedAccessTo _cachedAccessTo;
		public CustomersController(
			IHttpContextAccessor contextAccessor,
			IUserApplication userApplication,
			IEmailApplication emailApplication,
			IOrderApplication oa,
			IMapper mapper,
			NHibernate.ISession session,
			CachedAccessTo cachedAccessTo
		)
		{
			_contextAccessor = contextAccessor;
			_userApplication = userApplication;
			_emailApplication = emailApplication;
			_mapper = mapper;
			_session = session;
			_cachedAccessTo = cachedAccessTo;
			_orderApp = oa;
		}

		// called from staff pages to get a list of orders
		[HttpGet("")]
		//[ValidateActionParameters]
		[Produces(typeof(PagedResult<CustomerListDTO>))]
		public IActionResult GetAll([FromQuery] [Required] CustomerSearchCriteria sp, [FromServices] NHibernate.ISession session)
		{

			var customerid = 0;
			if (!string.IsNullOrEmpty(sp.Customer))
			{
				int.TryParse(sp.Customer.Trim(), out customerid);
			}


			Order order = null;
			if (!string.IsNullOrEmpty(sp.SortField))
			{
				order = new Order(sp.SortField, sp.SortDir == "true");
			}
			else
			{
				order = new Order("Id", true);
			}

			// OPTIMIZATION: Use the projection-optimized method for better performance
			var criteria = _userApplication.CustomerCriteria2WithProjection(sp.Customer, customerid, sp.OrderNr, sp.JobNr, sp.SystemAccess, sp.PaymentTerms, sp.IsPrintPortalEnabled, sp.ShowArchived,
				sp.PostalPostCode, sp.SalesConsultant , sp.CustomerStatus, sp.RegionLep, sp.FranchiseCode, sp.BusinessType, sp.Notes);

			if (sp.UnableToMeetPrice)
			{
				// SECURITY FIX: Use parameterized subquery instead of raw SQL
				var unableToMeetPriceSubquery = DetachedCriteria.For(typeof(IJob), "j")
					.CreateAlias("j.Order", "o")
					.Add(Restrictions.Eq("j.Status", JobStatusOptions.UnableToMeetPrice))
					.Add(Restrictions.EqProperty("o.Customer.Id", "cust.Id"))
					.SetProjection(Projections.Constant(1));

				criteria.Add(Subqueries.Exists(unableToMeetPriceSubquery));
			}

			// Add parent customer filter (main customers only, not sub-customers)
			criteria.Add(Restrictions.Eq("cust.ParentCustomer", null));


			var sortOrder = new[] { order };

			var list = Utils.GetPagedResult2(session, criteria, sp.Page, 20, x => new CustomerListDTO(x), sortOrder);

			return new OkObjectResult(list);
		}



		[HttpPost("csv")]
		[AllowAnonymous] // as we will authorize it by the token passed in Form
		public IActionResult GetCSV([FromForm] CustomerSearchCriteria sp, [FromServices] NHibernate.ISession session)
		{
			var x = Startup.ValidateJWT(sp.token);
			if (!x.IsInRole(LepRoles.Staff))
				return Unauthorized();


			//bool? systemAccess = null;
			//if (!string.IsNullOrEmpty(sp.SystemAccess))
			//{
			//	systemAccess = Convert.ToBoolean(sp.SystemAccess);
			//}

			//PaymentTermsOptions? paymentTerms = null;
			//if (!string.IsNullOrEmpty(sp.PaymentTerms))
			//{
			//	paymentTerms = (PaymentTermsOptions)Enum.Parse(typeof(PaymentTermsOptions), sp.PaymentTerms);
			//}

			/*
			Order order = null;
			if (!string.IsNullOrEmpty(sp.SortField))
			{
				order = new Order(sp.SortField, sp.SortDir == "true");
			}
			else
			{
				order = new Order("Id", true);
			}

			var criteria = session.CreateCriteria<ICustomerUser>("cust");

			criteria.AddOrder(new NHibernate.Criterion.Order("Name", true))
				.SetResultTransformer(new NHibernate.Transform.DistinctRootEntityResultTransformer());

			criteria.Add(Restrictions.Eq("cust.ParentCustomer", null));

			if (!string.IsNullOrEmpty(sp.PostalPostCode))
			{
				criteria.Add(Restrictions.Like("cust.PostalAddress.Postcode", sp.PostalPostCode, MatchMode.Start));
			}
			if (!string.IsNullOrEmpty(sp.SalesConsultant))
			{
				criteria.Add(Restrictions.Eq("cust.SalesConsultant", sp.SalesConsultant));
			}
			if (!string.IsNullOrEmpty(sp.CustomerStatus))
			{
				criteria.Add(Restrictions.Eq("cust.CustomerStatus", sp.CustomerStatus));
			}
			if (!string.IsNullOrEmpty(sp.RegionLep))
			{
				//LEFT OUTER JOIN SalesRegion AS sr ON c.BillingPostcode = sr.PostCode
				//criteria.Add(Restrictions.InG("PostalAddress.Postcode", sp.RegionLep));
				criteria.Add(Expression.Sql($@" ( this_1_.PostalPostcode in (select PostCode from SalesRegion where LEP_Region = '{sp.RegionLep}'))"));
			}

			if (!string.IsNullOrEmpty(sp.FranchiseCode))
			{
				criteria.Add(Restrictions.Eq("cust.FranchiseCode", sp.FranchiseCode));
			}
			*/
			var customerid = 0;
			if (!string.IsNullOrEmpty(sp.Customer))
			{
				int.TryParse(sp.Customer.Trim(), out customerid);
			}


			var criteria = _userApplication.CustomerCriteria2(sp.Customer, customerid, sp.OrderNr, sp.JobNr, true, sp.PaymentTerms, sp.IsPrintPortalEnabled, sp.ShowArchived,
							sp.PostalPostCode, sp.SalesConsultant, sp.CustomerStatus, sp.RegionLep, sp.FranchiseCode, null, sp.Notes);

			criteria.Add(Restrictions.In("cust.CustomerStatus", new string[] { "New", "Existing" , "Prospect" ,"First 3 Months" ,"Lapsed" , "Returned lapsed" , "Broker / Whale" }));

			var q = criteria.List<ICustomerUser>().Select(_ =>
			{

				var email = "";
				if (!String.IsNullOrEmpty(_.OtherEmail) && _.OtherEmail != "(null)" )
					email = _.OtherEmail;
				else if (!String.IsNullOrEmpty(_.Email) && _.Email != "(null)")
					email = _.Email;
				else if (!String.IsNullOrEmpty(_.Contact1.Email) && _.Contact1.Email != "(null)")
					email = _.Contact1.Email;

				return new
				{
					TradingName = _.Name,
					Email = email,
					ContactName = _.Contact1.Name,
					CustomerStatus = _.CustomerStatus,
					FranchiseCode = _.FranchiseCode,
					Id = _.CustomerNr,
					BillingPostCode = _.BillingAddress.Postcode,
					BusinessType = _.BusinessType,
					PaymentTerms = _.PaymentTerms,
					State = _.BillingAddress.State,
					PCode = _.ProductPriceCode,
					FCode = _.FreightPriceCode,
					LastOrderDate = _.LastOrderDate != null ? _.LastOrderDate.Value.ToString("dd/MM/yyyy") : "",

				};
			}
			).Where(_ => _.Email.IndexOf("Unsubscribed") == -1 );

			//var criteria = _userApplication.CustomerCriteria(customer, 0, orderid, jobid, systemAccess, paymentTerms, sp.IsPrintPortalEnabled);


			var suggestedName = $@"Customers - {sp.RegionLep} {sp.PostalPostCode} {sp.CustomerStatus} {sp.FranchiseCode} {System.DateTime.Now.Ticks}.csv";
			using (var sw = new StringWriter())
			using (var csvWriter = new CsvWriter(sw, CultureInfo.InvariantCulture))
			{
				csvWriter.WriteRecords(q);

				return File(Encoding.UTF8.GetBytes(sw.ToString()), "text/csv", suggestedName);
			}
		}




		[HttpGet("{id:int}")]
		[Produces(typeof(CustomerUserDto))]
		public IActionResult Get(int id)
		{
			ICustomerUser customer = null;

			if (id == 0)
			{
				customer = (lep.user.impl.CustomerUser)_userApplication.NewCustomerUser();
			}
			else
			{
				customer = _userApplication.GetCustomerUser(id);
				customer.LEPOnlineBalance = _userApplication.GetCustomerLEPOnlineBalance(id);
				customer.AvaiableBalance = _userApplication.GetCustomerAvailableBalance(id);

			}

			if (customer == null) return NotFound();
			var result = _mapper.Map<CustomerUserDto>(customer);
			result.ExtraFiles = _cachedAccessTo.CustomerExtraFiles(customer).ToList();

			result.DeliveryAddressProductionFaclity = _orderApp.GetProductionFacilityByPostCode(customer.PostalAddress.Postcode).ToString();

			result.Offers = _session.Query<CustomerOffer>()
						.Where(x => x.Customer.Id == id)
						.Where(x => x.DateTakenUp == null || (x.DateTakenUp< DateTime.Now && x.AllowReuse) )
						.Select(_mapper.Map<CustomerOfferDto>)
						.ToList();

			//result.CustomerNotes = _session.Query<CustomerNote>()
			//	.Where(x => x.Customer.Id == id)
			//	.OrderByDescending(x => x.CreatedOn)
			//	.Select(_mapper.Map<CustomerNoteDto>)
			//	.ToList();

			return new OkObjectResult(result);
		}



		[HttpGet("{id:int}/notes")]
		[Produces(typeof(List<CustomerNoteDto>))]
		public IActionResult GetNotes(int id)
		{
			ICustomerUser customer = null;
			customer = _userApplication.GetCustomerUser(id);
			if (customer == null) return NotFound();

			var notes = _session.Query<CustomerNote>()
				.Where(x => x.Customer.Id == id)
				.OrderByDescending(x => x.CreatedOn)
				.Select(_mapper.Map<CustomerNoteDto>)
				.ToList();

			try
			{
				string callRecordsQuery = $@"
					Select Id, IsDownloaded, CallDate, CallDateUTC, CallerNumber,
					DID, DisplayName, Direction, Participant1, Participant2,
					CLookupBy, CustomerId, CustomerName, CallerName
					from [CallList3CX] where CustomerId = {id} order by CallDate desc";
				IList<object[]> callRecords = _session.CreateSQLQuery(callRecordsQuery).List<object[]>();
				foreach (object[] cr in callRecords)
				{
					DateTime callDate = (DateTime)cr[2];
					string direction = (string)cr[7];
					string participant1 = (string)cr[8];
					string participant2 = (string)cr[9];
					int phoneRecordId = (int)cr[0];
					string caller = (string)cr[13];
					notes.Add(new CustomerNoteDto
					{
						NoteText = direction + " Call   from " + participant1 + "  -->  to  " + participant2,
						PhoneRecordId = phoneRecordId,
						CreatedOn = callDate,
						IsPhoneRecord = true
					});
				}
				notes = notes.OrderByDescending((CustomerNoteDto _) => _.CreatedOn).ToList();

			}
			catch (Exception ex)
			{
				var message = ex.Message;

			}
			string last10OrdersSql = $@"SELECT top 5 o.Id as OrderId, o.Status as OStatus, j.Id as  JobId, j.Status as JStatus

					FROM  [Order] o inner join Job j on o.Id = j.OrderId
					where o.UserId = {id}
					ORDER BY o.id desc, j.Id asc";
			dynamic last10Orders =  _session.CreateSQLQuery(last10OrdersSql).List<object[]>();

			dynamic result = new { notes, last10Orders };

			return new OkObjectResult(result);
		}


		[HttpPost("noteattachment/{id:int}")]
		[Produces(typeof(List<CustomerNoteDto>))]
		[AllowAnonymous]
		public IActionResult GetNotesAttachment(int id)
		{
			try
			{
				var sql = $"select filename,mimetype,filesize,documentBody from [PRD_AU_Notes].[dbo].customerNotes1 where Id={id}";
				var query = _session.CreateSQLQuery(sql);

				var r = query.List();
				if (r.Count == 0)
					return null;

				var f = (object[])r[0];
				var filename = (string)f[0];
				var mimetype = (string)f[1];
				//var filesize = (long)f[2];

				var documentBody = Convert.FromBase64String((string)f[3]);

				return new FileContentResult(documentBody, "application/pdf")
				{
					FileDownloadName = filename
				};
			}
			catch (Exception ex)
			{
				return null;
			}
		}


		[HttpPost("{id:int}/notes")]
		[Produces(typeof(List<CustomerNoteDto>))]
		public IActionResult PutNotes([FromRoute] int id)
		{
			ICustomerUser customer = null;
			customer = _userApplication.GetCustomerUser(id);
			if (customer == null) return NotFound();

			var cu = GetCurrentUser();

			dynamic d = JsonConvert.DeserializeObject<dynamic>(Request.Form["request"]);
			var noteText = (string)d.NoteText;


			if (d.Id == 0) // new note
			{
				var n = new CustomerNote()
				{
					Customer = customer,
					NoteText = noteText,
					CreatedBy = cu.FirstName + " " + cu.LastName,
					CreatedOn = DateTime.Now
				};

				string attachmentBody = string.Empty;
				if (Request.Form.Files.Count > 0)
				{
					IFormFile f = Request.Form.Files[0];
					using (var ms = new MemoryStream())
					{
						f.CopyTo(ms);
						var fileBytes = ms.ToArray();
						attachmentBody = Convert.ToBase64String(fileBytes);
					}
					n.IsDocument = true;
					n.FileName = f.FileName;
					n.MimeType = f.ContentType;
				}
				_session.SaveOrUpdate(n);
				_session.Refresh(n);

				// we do not map attactment body with Nhibernate as it would load too much data
				// so we use sql to update the attachment body
				var noteId = n.Id;
				if (attachmentBody != string.Empty)
				{
					var sql2 = $"update  [PRD_AU_Notes].[dbo].CustomerNotes1 set DocumentBody='{attachmentBody}' where Id = {noteId}";
					_session.CreateSQLQuery(sql2).ExecuteUpdate();
				}
			}
			else // we are updating a exting note's text
			{
				if (noteText != string.Empty)
				{
					var sql2 = $"update  [PRD_AU_Notes].[dbo].CustomerNotes1 set NoteText='{noteText}' where Id = {d.Id}";
					_session.CreateSQLQuery(sql2).ExecuteUpdate();
				}
			}

			var result = _session.Query<CustomerNote>()
				.Where(x => x.Customer.Id == id)
				.OrderByDescending(x => x.CreatedOn)
				.Select(_mapper.Map<CustomerNoteDto>)
				.ToList();

			return new OkObjectResult(result);
		}

		[HttpPut("{id:int}")]
		//[ValidateActionParameters]
		public IActionResult PutCustomer(int id, [FromBody] [Required] CustomerUserDto dto)
		{
			var customer = _userApplication.GetCustomerUser(dto.Id);
			if (customer == null)
			{
				customer = _userApplication.NewCustomerUser();
				customer.Username = dto.Username;
				var password = dto.Password ?? Utils.GeneratePassword(10);
				_userApplication.SetPassword(customer, password);
			}

			customer = _mapper.Map(dto, customer);
			customer.PreferredCourier = new lep.courier.CourierType(lep.courier.CourierType.None);
			customer.DateModified = DateTime.Now;
			_userApplication.Save(customer);
			Response.Headers.Add("Id", customer.Id.ToString());
			return new StatusCodeResult(id == 0 ? StatusCodes.Status201Created : StatusCodes.Status204NoContent);
		}

		[HttpPatch("{id:int}")]
		//[ValidateActionParameters]
		public IActionResult PatchCustomer(int id, [FromBody] [Required] JsonPatchDocument<CustomerUser> patchDoc)
		{
			var customer = (CustomerUser)_userApplication.GetCustomerUser(id);
			patchDoc.ApplyTo(customer);
			_userApplication.Save(customer);
			return Ok();
		}

		[ResponseCache(NoStore = true, Duration = 0)]
		[HttpGet("{id:int}/logo")]
		[AllowAnonymous]
		public IActionResult GetLogo([FromRoute]int id, [FromQuery] long? r = null)
		{
			var customer = _userApplication.GetUser(id);
			if (customer == null) return NotFound();
			var logoFile = _userApplication.GetCustomerLogo(customer.Username);
			if (logoFile == null) return NotFound();
			var result = new PhysicalFileResult(logoFile.FullName, "image/" + Path.GetExtension(logoFile.FullName).ToLower());
			return result;
		}

		[HttpPost("{id:int}/logo")]
		public IActionResult SaveCustLogo(int id)
		{
			var c = _userApplication.GetUser(id);
			if (c == null) return NotFound();

			var files = Request.Form.Files;
			if (files.Count() == 0)
			{
				return BadRequest();
			}

			try
			{
				var filename = ContentDispositionHeaderValue.Parse(files[0].ContentDisposition).FileName.Trim().ToString();
				var extension = Path.GetExtension(filename);
				IFormFile f = files[0];
				_userApplication.SaveCustomerLogo(c.Username, f.OpenReadStream(), extension);
				return Ok();
			}
			catch (Exception)
			{
				return BadRequest();
			}
		}

		/// Export On hold customers as csv
		[HttpPost("OnHoldExport")]
		public FileResult CustomersOnHoldExport()
		{
			CsvSerialiser serial = new CsvSerialiser();
			MemoryStream stream = new MemoryStream();
			StreamWriter csvWriter = new StreamWriter(stream, Encoding.UTF8);
			serial.Writer = csvWriter;
			_userApplication.ExportOnHold(serial);
			serial.EndDocument();
			stream.Position = 0;
			return File(stream, "text/csv", "onhold.csv");
		}

		/// Accept a file and import on hold customers
		[HttpPost("OnHoldImport")]
		public IActionResult CustomersOnHoldImport()
		{
			var files = Request.Form.Files;
			if (files.Count() == 0)
			{
				return BadRequest();
			}

			try
			{
				var filename = ContentDispositionHeaderValue.Parse(files[0].ContentDisposition).FileName.Trim();
				var extension = Path.GetExtension(filename.ToString());
				IFormFile f = files[0];
				//todo:                 _userApplication.UpdateOnHold()

				return Ok();
			}
			catch (Exception)
			{
				return BadRequest();
			}
		}

		[ReturnBadRequestOnModelError]
		[HttpPost("{id:int}/merge")]
		public IActionResult MergeTo([FromRoute]int id, [FromBody] string toCust)
		{
			var customer = (CustomerUser)_userApplication.GetCustomerUser(id);
			if (customer == null) return NotFound();

			ICustomerUser to = _userApplication.GetCustomerUser(toCust); ;
			if (to == null)
			{
				return new BadRequestObjectResult(new { error = "Can not Merge" });
			}

			try
			{
				if (_userApplication.NeedMergeCustomer(customer))
				{
					_userApplication.MergeCustomer(customer, to);
				}
				_userApplication.Delete(customer);

				return new OkObjectResult(new { Id = to.Id });
			}
			catch (Exception)
			{
				return BadRequest();
			}
		}

		[HttpDelete("{id:int}")]
		public IActionResult Delete(int id) //int? Id = null
		{
			var customer = _userApplication.GetUser(id);
			if (customer == null) return NotFound();
			_userApplication.Delete(customer);
			return Ok();
		}

		[HttpPost("IsValidUsername")]
		public IActionResult IsUniqueMYOB(int userId, string userName)
		{
			var user = _userApplication.GetUser(userId);
			var valid = _userApplication.IsValidUsername(userName, user);
			return new OkObjectResult(new { Valid = valid });
		}



		[HttpGet("CustomerLookups")]
		public IActionResult GetCustomerLookups()
		{
			IList result = _userApplication.GetCustomerProductPricing();
			var CustomerProductPricing = new Dictionary<string, string>();
			foreach (object[] r in result)
			{
				CustomerProductPricing.Add((string)r[0], (string)r[1]);
			}


			result = _userApplication.GetCustomerFreightPricing();
			var CustomerFreightPricing = new Dictionary<string, string>();

			foreach (object[] r in result)
			{
				CustomerFreightPricing.Add((string)r[0], (string)r[1]);
			}

			var CustomerStatus = _session.CreateSQLQuery("Select CustomerStatus From CustomerStatus2 order by 1").List<String>();
			var Franchise  = _session.CreateSQLQuery("Select Code, Name From Franchise order by 1")
					.List<object[]>()
					.Select(x => new { code = (string)x[0], name = (string)x[1] });

			var SalesConsultant =  _session.CreateSQLQuery("Select Name,Name From SalesConsultant order by 1").List<object[]>()
				.Select(x => new { Id = x[0].ToString(), Name = (string)x[0] }); ;



			var BusinessTypes = _session.CreateSQLQuery("Select Title, Title From CustomerBusinessTypes order by 1").List<object[]>()
				.Select(x => new { Id = (string)x[0], Name = (string)x[0] }); ;

			var RegionLep = _session.CreateSQLQuery("SELECT distinct [LEP_Region] FROM  [SalesRegion] order by 1").List<string>();
			var RegionAP = _session.CreateSQLQuery("SELECT distinct   [Aust_Post_Region]  FROM  [SalesRegion] order by 1").List<string>();

			return new OkObjectResult( new { CustomerProductPricing , CustomerFreightPricing, CustomerStatus , Franchise , SalesConsultant , BusinessTypes, RegionLep, RegionAP });
		}


		[HttpGet("CustomerProductPricing")]
		public IActionResult GetCustomerProductPricing()
		{
			IList result = _userApplication.GetCustomerProductPricing();
			var result2 = new Dictionary<string, string>();

			foreach (object[] r in result)
			{
				result2.Add((string)r[0], (string)r[1]);
			}

			return new OkObjectResult(result2);
		}

		[HttpGet("CustomerFreightPricing")]
		public IActionResult GetCustomerFreightPricing()
		{
			var result = _userApplication.GetCustomerFreightPricing();
			var result2 = new Dictionary<string, string>();

			foreach (object[] r in result)
			{
				result2.Add((string)r[0], (string)r[1]);
			}

			return new OkObjectResult(result2);
		}

		[HttpGet("CustomerStatus")]
		public IActionResult GetCustomerStatus()
		{
			var q = _session.CreateSQLQuery("Select CustomerStatus From CustomerStatus2 order by 1").List<String>();
			return new OkObjectResult(q);
		}

		[HttpGet("Franchise")]
		public IActionResult GetFranchiseCode()
		{
			var q = _session.CreateSQLQuery("Select Code, Name From Franchise order by 1")
					.List<object[]>()
					.Select(x => new { code = (string)x[0], name = (string)x[1] });
			return new OkObjectResult(q);
		}

		[HttpGet("SalesConsultant")]
		public IActionResult GetSalesConsultant()
		{
			var q = _session.CreateSQLQuery("Select Name From SalesConsultant order by 1").List<String>();
			return new OkObjectResult(q);
		}

		[HttpGet("BusinessTypes")]
		public IActionResult GetCustomerBusinessTypes()
		{
			var q = _session.CreateSQLQuery("Select Title From CustomerBusinessTypes order by 1").List<String>();
			return new OkObjectResult(q);
		}

		protected IStaff GetCurrentUser()
		{
			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			return (IStaff)_userApplication.GetUser(userId);
		}

	}
}
