namespace lumen.hibernate.type
{
	using NHibernate.Dialect;
	using NHibernate.Engine;
	using NHibernate.SqlTypes;
	using NHibernate.Type;
	using System;
	using System.Data;
	using System.Data.Common;
	using SqlDateTime = System.Data.SqlTypes.SqlDateTime;
	[Serializable]
	public class DateTimeType : PrimitiveType, IIdentifierType, ILiteralType
	{
		public DateTimeType() : base(SqlTypeFactory.DateTime)
		{
		}

		public override object DefaultValue => DateTime.MinValue;
		public override string Name => "DateTime";
		public override Type PrimitiveClass => typeof(DateTime);
		public override Type ReturnedClass => typeof(DateTime);
		[Obsolete]
		public override object FromStringValue(string xml) => DateTime.Parse(xml);


		public override object Get(DbDataReader rs, string name, ISessionImplementor session)
		{
			return Get(rs, rs.GetOrdinal(name), session); // rs.[name];
		}

		public override object Get(DbDataReader rs, int index, ISessionImplementor session)
		{
			DateTime dbValue = Convert.ToDateTime(rs[index]);
			return new DateTime(dbValue.Year, dbValue.Month, dbValue.Day, dbValue.Hour, dbValue.Minute, dbValue.Second);
		}

		public override string ObjectToSQLString(object value, Dialect dialect)
		{
			return "'" + value.ToString().ToLower() + "'";
		}

		public override void Set(DbCommand cmd, object value, int index, ISessionImplementor session)
		{
			DateTime date = (DateTime)value;
			if (date <= SqlDateTime.MinValue.Value)
			{
				base.NullSafeSet(cmd, null, index, session);
			}
			else
			{
				IDataParameter parm = cmd.Parameters[index] as IDataParameter;
				DateTime dateValue = (DateTime)value;
				parm.Value = new DateTime(dateValue.Year, dateValue.Month, dateValue.Day, dateValue.Hour, dateValue.Minute, dateValue.Second);
			}
		}

		public object StringToObject(string xml) => DateTime.Parse(xml);

	}
	/*

		public class DateTimeType0: PrimitiveType,IIdentifierType,ILiteralType
	{
		public DateTimeType() : base( SqlTypeFactory.DateTime )
		{
		}


		public override object DefaultValue
		{
			get { return DateTime.MinValue; }
		}

		public override System.Type PrimitiveClass
		{
			get { return typeof( DateTime ); }
		}

		public override string ObjectToSQLString( object value,Dialect dialect )
		{
			return "'" + value.ToString().ToLower() + "'";
		}

		public override object NullSafeGet( IDataReader rs, string name )
		{
			object o = base.NullSafeGet( rs, name );
			if (o == null) {
				return DateTime.MinValue;

			}

			DateTime date = (DateTime) o;
			if (date.Year == DateTime.MaxValue.Year &&
				date.Month == DateTime.MaxValue.Month &&
				date.Day == DateTime.MaxValue.Day &&
				date.Hour == DateTime.MaxValue.Hour &&
				date.Minute == DateTime.MaxValue.Minute &&
				date.Second == DateTime.MaxValue.Second) {

				return DateTime.MaxValue;
			}

			return o;
		}

		public override object Get( IDataReader rs, int index )
		{
			DateTime dbValue = Convert.ToDateTime( rs[ index ] );
			return new DateTime( dbValue.Year, dbValue.Month, dbValue.Day, dbValue.Hour, dbValue.Minute, dbValue.Second );
		}

		public override object Get( IDataReader rs, string name )
		{
			return Get( rs, rs.GetOrdinal( name ) ); // rs.[name];
		}

		public override System.Type ReturnedClass
		{
			get { return typeof( DateTime ); }
		}

		public override void Set( IDbCommand st, object value, int index )
		{
			DateTime date = (DateTime) value;
			if (date <= SqlDateTime.MinValue.Value) {
				base.NullSafeSet( st, null, index);

			} else {
				IDataParameter parm = st.Parameters[ index ] as IDataParameter;
				DateTime dateValue = ( DateTime ) value;
				parm.Value = new DateTime( dateValue.Year, dateValue.Month, dateValue.Day, dateValue.Hour, dateValue.Minute, dateValue.Second );
			}
		}

		public override string Name
		{
			get { return "DateTime"; }
		}

		public override string ToString( object val )
		{
			return ((DateTime) val ).ToShortDateString();
		}

		public object StringToObject( string xml )
		{
			return DateTime.Parse( xml );
		}

		public override object FromStringValue( string xml )
		{
			return DateTime.Parse( xml );
		}

		public object Next( object current )
		{
			return Seed;
		}

		public object Seed
		{
			get { return DateTime.Now; }
		}

		public IComparer Comparator
		{
			get { return Comparer.DefaultInvariant; }
		}
	}

	*/
}
