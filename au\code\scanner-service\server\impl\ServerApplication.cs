namespace lep.server.impl
{
	using RestSharp;
	using System;
	using System.Collections;
	using System.Net;
	using System.Threading;
	using Serilog;

	public class ServerApplication : IServerApplication
	{
		#region my data

		private Queue queue;
		private Timer timer;

		#endregion my data

		private Common.Logging.ILog log;

		public void Start()
		{
			log = Common.Logging.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

			queue = Queue.Synchronized(new Queue());

			ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

			timer = new Timer(new TimerCallback(Run), null, new TimeSpan(0, 0, 0), Interval);
		}

		public void Stop()
		{
			lock (this)
			{
				timer.Dispose();
			}
		}

		public void ScanBarcode(string scanner, string barcode, DateTime time)
		{
			queue.Enqueue(new Tuple(scanner, barcode, time));

			if (log.IsInfoEnabled)
			{
				Log.Information(string.Format("scanner={0} barcode={1} ", scanner, barcode));
			}
		}

		public void Run(object state)
		{
			lock (this)
			{
				while (0 != queue.Count)
				{
					Tuple t = (Tuple)queue.Peek();

					var host = ServerHost;
					if (string.IsNullOrEmpty(host))
					{
						Log.Error("no server");
						break;
					}

					try
					{
						var client = new RestSharp.RestClient(host);
						client.CookieContainer = new CookieContainer();

						var loginRequest = new RestSharp.RestRequest("/api/Account/Login", Method.POST)
										.AddJsonBody(new { Username = Username, Password = Password });
						var loginResult = client.Execute(loginRequest);
						if (loginResult == null || loginResult.StatusCode != HttpStatusCode.OK)
						{
							Log.Error("login failed {username}");
							Thread.Sleep(new TimeSpan(0, 0, 1));
							continue;
						}

						var request = new RestRequest("/api/barcode/scan", Method.POST)
							.AddJsonBody(new { Scanner = t.scanner, Barcode = t.barcode, Time = t.time });
						IRestResponse<ScanResponse> result = client.Execute<ScanResponse>(request);
						if (result.ResponseStatus == ResponseStatus.Completed)
						{
							Log.Information($"scan {t.scanner} {t.barcode}, {result.Data.Data}");
							queue.Dequeue();
						}
					}
					catch (Exception e)
					{
						Log.Error(string.Format("server error: {0}", e.Message), e);
						break;
					}
				}
			}
		}

		private class ScanResponse
		{
			public string Data { get; set; }
		}

		private class Tuple
		{
			public string scanner;
			public string barcode;
			public DateTime time;

			public Tuple(string scanner, string barcode, DateTime time)
			{
				this.scanner = scanner;
				this.barcode = barcode;
				this.time = time;
			}
		}

		#region properties

		public string ServerHost
		{ set; private get; }

		public string Username
		{ set; private get; }

		public string Password
		{ set; private get; }

		public TimeSpan Interval
		{ set; private get; } = new TimeSpan(0, 1, 0);
	}

	#endregion properties
}
