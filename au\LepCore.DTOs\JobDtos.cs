using AutoMapper;
using lep;
using lep.address.impl;
using lep.extensionmethods;
using lep.job;
using lep.job.impl;
using lep.user;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using static lep.job.JobTypeOptions;

namespace LepCore.Dto
{
	public class RunJobSummaryDto : JobSummaryDto, IHaveCustomMappings
	{
		public int[] Slots { get; set; }
		public int NumberOfSlots { get; set; }

		public int BaseSpace { get; set; }

		public SizeDto FinishedSize { get; set; }
		public string FinishedSizeStr { get; set; }

		public SizeDto FoldedSize { get; set; }

		public RoundOption RoundOption { get; set; }
		public RoundDetailOption RoundDetailOption { get; set; }


		public JobCelloglazeOptions FrontCelloglaze { get; set; }
		public JobCelloglazeOptions BackCelloglaze { get; set; }
		public string FoilColour { get; set; }

		public int? NumberOfHoles { get; set; }
		public HoleDrilling HoleDrilling { get; set; }

		public RunJobSummaryDto()
		{
		}

		public string OrderCustomerName { get; set; }
		public string OrderCustomerContact1Name { get; set; }
		public string OrderCustomerContact1Phone { get; set; }

		public string Comment { get; set; }

		public string Corners { get; set; }
		public string CustomDieCut { get; set; }
		public bool Scoring { get; set; }
		public string ScoringInstructions { get; set; }

		public bool IsReprint { get; set; }
		public bool IsRestart { get; set; }

		public new void CreateMappings(Profile profile)
		{
			profile.CreateMap<Job, RunJobSummaryDto>()
			 .AfterMap((j, d) =>
			 {
				 d.BaseSpace = j.GetBaseSpace();
				 d.Comment = j.SpecialInstructions;
				 //if (!j.Order.DeliveryAddress.Equals(j.Order.Customer.PostalAddress))
				 //{
					// d.Comment +=
					//	string.Format("\r\n{0} {1}\r\n{2}\r\n{3} {4}\r\n{5}\r\n{6}, {7}, {8}",
					//	j.Order.RecipientName, j.Order.RecipientPhone, j.Order.DeliveryAddress.Address1, j.Order.DeliveryAddress.Address2, j.Order.DeliveryAddress.Address3, j.Order.DeliveryAddress.City, j.Order.DeliveryAddress.Postcode, j.Order.DeliveryAddress.State, j.Order.DeliveryAddress.Country);
				 //}
				 d.Corners = string.Concat(
					j.TLround ? "tl" : string.Empty,
					j.TRround ? "tr" : string.Empty,
					j.BLround ? "bl" : string.Empty,
					j.BRround ? "br" : string.Empty);
				 d.FinishedSizeStr = j.GetJobSize();
			 });
		}
	}

	//public class JobSummaryFreightDto : JobSummaryDto, IMapFrom<Job>
	//   {
	//       public Freight Freight { get; set; }
	//   }

	//
	public class CustomerJobSummaryDto : IHaveCustomMappings
	{
		public CustomerJobSummaryDto()
		{
		}

		public int Id { get; set; }
		public string Name { get; set; }
		public int Quantity { get; set; }
		public int TemplateId { get; set; }
		public JobStatusOptions Status { get; set; }
		public int P { get; set; }
		public JobApprovalOptions SupplyArtworkApproval { set; get; }
		public JobApprovalOptions ReadyArtworkApproval { set; get; }
		public bool QuoteNeedApprove { get; set; }

		public List<string> Thumbs { get; set; }

		public string StatusCss { get; set; }

		public DateTime DateModified { get; set; }

		public void CreateMappings(Profile profile)
		{
			profile.CreateMap<Job, CustomerJobSummaryDto>();
		}
	}

	public class JobPrintDto : IHaveCustomMappings
	{
		public JobPrintDto()
		{
		}

		public int Id { get; set; }
		public DateTime DateModified { get; set; }
		public string Thumb0 { get; set; }
		public string Thumb1 { get; set; }

		public int ColorSides { get; set; }
		public string Name { get; set; }
		public string Category { get; set; }
		public JobTemplateDto Template { get; set; } = new JobTemplateDto();
		public string FinishedSize { get; set; }
		public string FoldedSize { get; set; }
		public StockDto Stock { get; set; }
		public StockDto StockOverride { get; set; }
		public string Rotation { get; set; }

		// string ArtSuppliedVia { get; set; }
		public List<ArtworkDto> Artworks { get; set; }

		// public IList<IComment> Comments { get; set; }

		public string ArtworkStatus { get; set; }
		public string BindingOption { get; set; }
		public string CustomDieCut { get; set; }
		public int CustomSlot { get; set; } = 1;
		public string DieCutting { get; set; }
		public string DieCutType { get; set; }
		public string FrontCelloglaze { get; set; }
		public string BackCelloglaze { get; set; }

		public string FrontCelloglazeOverride { get; set; }
		public string BackCelloglazeOverride { get; set; }

		public string FoilColour { get; set; }
		public string Envelope { get; set; }
		public string EnvelopeType { get; set; }
		public string FrontPrinting { get; set; }
		public string BackPrinting { get; set; }
		public int? NumberOfHoles { get; set; } = null;
		public string HoleDrilling { get; set; }
		public bool NCRNumbered { get; set; }
		public string NCRStartingNumber { get; set; }
		// round
		public bool TLround { get; set; }

		public bool TRround { get; set; }
		public bool BLround { get; set; }
		public bool BRround { get; set; }
		public string RoundOption { get; set; }
		public string RoundDetailOption { get; set; }
		public bool TrackProgress { get; set; }
		public bool Urgent { get; set; }

		public string Folding { get; set; }
		public string PrintType { get; set; }
		public string ForcedPrintType { get; set; }
		public decimal Price { get; set; }
		public virtual bool IsWhiteLabel { get; set; }
		public virtual string PriceWL { get; set; }
		public bool Scoring { get; set; }
		public string ScoringInstructions { get; set; }
		public bool SelfCovered { get; set; }
		public bool SendSamples { get; set; }
		public string SpecialInstructions { get; set; }
		public JobStatusOptions Status { get; set; }

		public string StatusC { get; set; }
		public string StatusCss { get; set; }

		public string StockForCover { get; set; }
		public string StockForCoverOverride { get; set; }

		public bool Magnet { get; set; }
		public int NumberOfMagnets { get; set; }

		public int Pages { get; set; }
		public bool Perforating { get; set; }
		public string PerforatingInstructions { get; set; }

		public int Quantity { get; set; }

		public bool IsAutoPriceExpired { get; set; }

		public bool IsQuoteExpired { get; set; }
		public bool IsQuotePrice { get; set; }
		private bool IsReprint { get; set; }
		private bool IsReprintJobDispatch { get; set; }

		public string PadDirection { get; set; }

		//string Preview { get; set; }
		public DateTime PriceDate { get; set; }

		public string ProductionInstructions { get; set; }
		//string ProductPriceCode { get; set; }

		//
		public bool QuoteNeedApprove { get; set; }

		//

		//string ReprintCost { get; set; }
		//int ReprintFromPreviousRunNo { get; set; }
		//string ReprintReason { get; set; }
		//string ReprintReasonPredefined { get; set; }
		//string ReprintResult { get; set; }
		//string RequestedPackaging { get; set; }

		#region staff only fields

		public DateTime? RequiredByDate { get; set; }
		public string LepSpecialInstructions { get; set; }

		public bool InvolvesOutwork { get; set; }
		public string PricedOnValidTill { get; set; }

		#endregion staff only fields

		public List<KeyValuePair<string, string>> Properties = new();

		public void CreateMappings(Profile profile)
		{
			profile.CreateMap<Job, JobPrintDto>()
				.ForMember(s => s.Price, _ => _.Ignore())
				.ForMember(d => d.Stock, _ => _.MapFrom(s => s.FinalStock))
				.AfterMap((s, d) =>
				{
					Action<string, object> x = (k, v) =>
					 {
						 if (v == null)
							 return;

						 if (v is Enum && (int)v != 0)
						 {
							 d.Properties.Add(new KeyValuePair<string, string>(k, v.ToString()));
						 }
						 else if (v is Boolean && (bool)v == true)
						 {
							 d.Properties.Add(new KeyValuePair<string, string>(k, "Yes"));
						 }
						 else if ((v is String && !string.IsNullOrEmpty((string)v) && ((string)v != "None")))
						 {
							 d.Properties.Add(new KeyValuePair<string, string>(k, v.ToString()));
						 }
						 else if (v is Decimal && (Decimal)v != 0.0m)
						 {
							 d.Properties.Add(new KeyValuePair<string, string>(k, v.ToString()));
						 }
						 else if (!(v is String) && !(v is Enum) && !(v is bool) && (int)v != 0)
						 {
							 d.Properties.Add(new KeyValuePair<string, string>(k, v.ToString()));
						 }
					 };

					Action<string, object> x0 = (k, v) =>
					{
						if (v is Enum)
						{
							d.Properties.Add(new KeyValuePair<string, string>(k, v.ToString()));
						}
						else if (v is Boolean)
						{
							d.Properties.Add(new KeyValuePair<string, string>(k, (((bool)v) ? "Yes" : "No")));
						}
						else if (v is String)
						{
							d.Properties.Add(new KeyValuePair<string, string>(k, v.ToString()));
						}
						else if (!(v is String) && !(v is Enum) && !(v is bool))
						{
							d.Properties.Add(new KeyValuePair<string, string>(k, v.ToString()));
						}
					};

					try
					{
						d.Price = decimal.Parse(s.Price);
					}
					catch
					{
						d.Price = 0;
					}

					d.PrintType = s.PrintType.ToDescription();
					d.FinishedSize = s.GetJobSize();
					d.FoldedSize = s.GetJobFoldedSize();
					d.StockForCover = s.StockForCover?.Name ?? null;

					if (!s.IsEnvelope())
					{
						x("Front/Back", $"{s.FrontPrinting.ToDescription()}/{s.BackPrinting.ToDescription()}");
					}

					x("Folding", s.GetJobFoldedSize());

					//x0("Front Cello", s.FrontCelloglaze.ToDescription());
					//x0("Back Cello", s.BackCelloglaze.ToDescription());
					if (!s.IsEnvelope())
					{
						x0("Cello", CelloUtils.GetCelloName(s.FinalFrontCelloglaze, s.FinalBackCelloglaze, true));
					}


					x("Round option", s.RoundOption);
					if (((int)s.RoundOption).IsNot(0, 3, 5))
					{
						x("Round details", s.RoundDetailOption.ToDescription());
					}

					x("Hole drilling", s.HoleDrilling.ToDescription());


					x("# of Holes ", s.NumberOfHoles);

					x("Finished by", s.PadDirection.ToDescription());


					var spineWidth = s.GetSpineWidth();
					string spineWidthStr = spineWidth == 0.0m ? null : $"{spineWidth}mm";

					if (s.Template.Is(MagazineNDD, Magazine, A4CalendarSelfCover))
					{
						//x("Cover", s.StockForCover?.Name ?? null);
						x("Binding", s.BindingOption?.Name ?? null);
						x("Orientation", s.Rotation.ToDescription());
						x("Bound On", s.BoundEdge.ToDescription());
						x("Spine width", spineWidthStr);
						x("# of Pages", s.Pages + " including cover");
					}
					if (s.Template.Is(MagazineSeparate, A4CalendarSeparateCover))
					{
						x("Cover", s.StockForCover?.Name ?? null);
						x("Binding", s.BindingOption?.Name ?? null);
						x("Orientation", s.Rotation.ToDescription());
						x("Bound On", s.BoundEdge.ToDescription());
						x("Spine width", spineWidthStr);
						x("# of Pages", s.Pages + " excluding cover");
					}

					if (s.Template.Is(Notepads))
					{
						x("# of Sheets/pad", s.Pages);
					}

					if (s.Template.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks))
					{
						x("Bound On", s.BoundEdge.ToDescription());
						x("# of Sheets/pad", s.Pages);
						x("Numbered", s.NCRNumbered);
						x("Starting number", s.NCRStartingNumber);

						if (s.NCRInfo != null)
						{
							foreach (char c in "1234")
							{
								NCRSheetInfo si;
								if (s.NCRInfo.Sheets.TryGetValue(c.ToString(), out si))
								{
									x($"Sheet {c} Color", si.PaperColor);
									x0("...Perforated", si.Perforated);
									x0("...Back Print", si.PrintedOnBack);
								}
							}
							x("Cover", s.NCRInfo.Cover);
							x("Binding Tape", s.NCRInfo.BindingTape);
						}

					}

					x("Foil colour", s.FoilColour);
					x("Envelope", s.Envelope);

					if (s.Template.Is(PresentationFolder, PresentationFolderNDD))
					{
						x("Die cutting", s.DieCutType.ToDescription());
					}

					x("Perforating", s.Perforating);
					x("Scoring", s.Scoring);
					x("Magnet", s.NumberOfMagnets);

					if (s.SendSamples)
					{
						x("Samples", "Send");
					}

					if (s.IsQuotePrice)
					{
						d.PricedOnValidTill = $"Priced on {s.PriceDate.Value.ToShortDateString()}, valid till {s.PriceDate.Value.AddDays(60).ToShortDateString()}";
					}
				});
		}
	}

	public class JobSummaryDto : IHaveCustomMappings
	{
		public JobSummaryDto()
		{
		}

		DateTime DateCreated { get; set; }
		public string HealthCss { get; set; }

		public int Id { get; set; }
		public int OrderId { get; set; }

		public List<int> RunIds { get; set; }

		// public string JobNr { get; set; }
		public string Name { get; set; }

		public int TemplateId { get; set; }
		public string TemplateName { get; set; }
		public JobStatusOptions Status { get; set; }

		public OrderStatusOptions OrderStatus { get; set; }
		public bool SendSamples { get; set; }
		public int Quantity { get; set; }
		public string Price { get; set; }
		public string PriceWL { get; set; }
		public PrintType PrintType { get; set; }
		public virtual Facility? Facility { get; set; }

		public string StatusS { get; set; }
		public string StatusC { get; set; }
		public string StatusCss { get; set; }

		public string JobInfo { get; set; }
		public bool Enable { get; set; }

		public bool HasReject { get; }
		public bool NeedApproval { get; }

		// Freight removed as not required
		//public Freight Freight { get; set; }
		//public List<JobStatusOptions> RoutingChain { get; set; }
		//public string RoutingChainTxt { get; set; }
		//public List<JobStatusOptions> Route { get; set; }
		//public int P { get; set; }

		public JobApprovalOptions SupplyArtworkApproval { set; get; }
		public JobApprovalOptions ReadyArtworkApproval { set; get; }
		public ArtworkStatusOption ArtworkStatus { get; set; }
		public bool QuoteNeedApprove { get; set; }

		public DateTime DateModified { get; set; }

		public dynamic Visibility { get; set; } = new ExpandoObject();

		public IList<string> RequiredPositions { get; set; }

		public bool? AACNotPerformed { get; set; }

		// Splits
		public JobSplits Splits { get; set; } = new JobSplits();

		public void CreateMappings(Profile profile)
		{
			profile.CreateMap<Job, JobSummaryDto>().AfterMap((s, d) =>
			{
				d.RunIds = s.Runs.Select(r => r.Id).ToList();
				d.RequiredPositions = s.GetRequiredPosition();
				d.AACNotPerformed = s.AACNotPerformed ?? false;
				//var list = StandardRouting.Instance.GetNextRoutesForJob(sj).Select(x => x.ToString()).ToArray();
				//dto.RoutingChainTxt = string.Join( ",", list);
				//dto.Route = StandardRouting.Instance.GetNextRoutesForJob(sj);

				//var countOfStepsDone = dto.Route.Count(x => x <= sj.Status);
				//dto.P = (int)((countOfStepsDone / (float)dto.Route.Count()) * 100);
			});
		}
	}

	public class TrimmedTemplate : JobTemplate, IMapFrom<JobTemplate>, IMapTo<JobTemplate>
	{
	}

	public class ArtworkDto : IMapFrom<Artwork>
	{
		public int Id { get; set; }
		public ArtworkTypeOptions Type { get; set; }
		public string Supplied { get; set; } = "";
		public string Ready { get; set; } = null;
		public string Preview { get; set; } = null;
		public string Position { get; set; }

		//public string Comment { get; set; }
		//public string Url { get; set; }
	}

	public class CommentDto : IMapFrom<Comment>
	{
		//public int Id { get; set; }
		public string CommentText { get; set; }

		public DateTime CreationDate { get; set; }
		public UserDto Author { get; set; }
		public bool LepOnly { get; set; }
		public bool? AACPerformed { get; set; }
	}

	public class WiroMagazineInfoDto : WiroMagazineInfo, IHaveCustomMappings
	{
		public new StockDto InnerFrontStockForCover { get; set; }
		public new StockDto InnerBackStockForCover { get; set; }

		public void CreateMappings(Profile profile)
		{
			profile.CreateMap<WiroMagazineInfo, WiroMagazineInfoDto>();
			profile.CreateMap<WiroMagazineInfoDto, WiroMagazineInfo>();
		}
	}

	public class JobViewCustDto : IHaveCustomMappings
	{
		public JobViewCustDto()
		{
			Template = new JobTemplateDto();

			//FinishedSize = new Size();
			//FinishedSize.PaperSize = new PaperSize();
			//FinishedSize.PaperSize.Size = new Size();

			//FinishedSize.PaperSize.FreightPaperSize = new PaperSize();
			//FinishedSize.PaperSize.FreightPaperSize.Size = new Size();

			//FoldedSize = new Size();
			//FoldedSize.PaperSize = new PaperSize();
			//FoldedSize.PaperSize.Size = new Size();

			//FoldedSize.PaperSize.FreightPaperSize = new PaperSize();
			//FoldedSize.PaperSize.FreightPaperSize.Size = new Size();

			BindingOption = new BindingOption();

			Stock = new StockDto();
			StockOverride = new StockDto();

			StockForCover = new Stock();
			StockForCoverOverride = new Stock();

			WiroInfo = new WiroMagazineInfoDto();

			WiroInfo.InnerFrontStockForCover = new StockDto();
			WiroInfo.InnerBackStockForCover = new StockDto();

			//  Artworks = new List<IArtwork>();
		}


		public UserDto CreatedBy { get; set; }
		public DateTime DateCreated { get; set; }
		public DateTime DateModified { get; set; }

		public int OrderId { get; set; }
		public int OrderCustomerId { get; set; }
		public int OrderWLCustomerId { get; set; }

		public bool Enabled { get; set; }
		//public List<AttachmentViewModel> Attachments { get; set; } = new List<AttachmentViewModel>();

		public List<Attachments> AttachmentsToProcess { get; set; } = new List<Attachments>();

		public List<CommentDto> Comments { get; set; } = new List<CommentDto>();

		public List<CommentDto> CommentsToAdd { get; set; } = new List<CommentDto>();

		#region special fields

		public string UploadType { get; set; }

		//public string Country  { get; set; }
		//public string State    { get; set; }
		//public string PostCode { get; set; }
		//public string Suburb   { get; set; }
		public PhysicalAddress DeliveryAddress { get; set; } = new PhysicalAddress();

		public int SpecStockId { get; set; }
		public int ColorSides { get; set; }

		#endregion special fields

		public dynamic Visibility { get; set; } = new ExpandoObject();

		public List<string> RequiredPositions { get; set; }

		public int Id { get; set; }
		public string JobNr { get; set; }
		public string Name { get; set; }
		public string Category { get; set; }
		public JobTemplateDto Template { get; set; }

		public SizeDto /*ISize*/  FinishedSize { get; set; } = new SizeDto();
		public SizeDto /*ISize*/  FoldedSize { get; set; } = new SizeDto();

		public StockDto Stock { get; set; }
		public StockDto StockOverride { get; set; }

		public RotationOption Rotation { get; set; }
		public JobBoundEdgeOptions BoundEdge { get; set; }

		// string ArtSuppliedVia { get; set; }
		public List<ArtworkDto> Artworks { get; set; }

		// public IList<IComment> Comments { get; set; }

		public ArtworkStatusOption ArtworkStatus { get; set; }

		public IBindingOption BindingOption { get; set; }

		public string CustomDieCut { get; set; }

		public int CustomSlot { get; set; } = 1;

		// public DateTime DateCreated { get; set; }
		// public DateTime DateModified { get; set; }
		public string DieCutting { get; set; }

		public CutOptions DieCutType { get; set; }
		public DateTime? DispatchDate { get; set; }

		public DateTime? PrintByDate { get; set; }
		private string DispatchRequirements { get; set; }

		// public bool Enable { get; set; }
		public bool IsCustomFacility { get; set; }

		public Facility? Facility { get; set; }

		public DateTime FinishedDate { get; set; }

		public JobCelloglazeOptions FrontCelloglaze { get; set; }
		public JobCelloglazeOptions BackCelloglaze { get; set; }

		public JobCelloglazeOptions? FrontCelloglazeOverride { get; set; }
		public JobCelloglazeOptions? BackCelloglazeOverride { get; set; }
		

		public string FoilColour { get; set; }
		public string Envelope { get; set; }
		public string EnvelopeType { get; set; }

		public JobPrintOptions FrontPrinting { get; set; }
		public JobPrintOptions BackPrinting { get; set; }
		public int? NumberOfHoles { get; set; } = null;
		public HoleDrilling HoleDrilling { get; set; }

		// round
		public bool TLround { get; set; }

		public bool TRround { get; set; }
		public bool BLround { get; set; }
		public bool BRround { get; set; }
		public RoundOption RoundOption { get; set; }
		public RoundDetailOption RoundDetailOption { get; set; }

		public bool TrackProgress { get; set; }
		public bool Urgent { get; set; }

		public string Folding { get; set; }

		public PrintType PrintType { get; set; }
		public PrintType? ForcedPrintType { get; set; }
		public string Price { get; set; }

		public virtual bool IsWhiteLabel { get; set; }
		public virtual string PriceWL { get; set; }

		public decimal? CustomerRequestedPrice { get; set; }

		//   public int ScanCount { get; set; }
		public bool Scoring { get; set; }

		public string ScoringInstructions { get; set; }
		public bool SelfCovered { get; set; }
		public bool SendSamples { get; set; }

		public bool NCRNumbered { get; set; }
		public string NCRStartingNumber { get; set; }
		public NCRInfo NCRInfo { get; set; }
		public WiroMagazineInfoDto WiroInfo { get; set; }
		public string SpecialInstructions { get; set; }
		public JobStatusOptions Status { get; set; }

		public string StatusC { get; set; }
		public string StatusCss { get; set; }

		public OrderStatusOptions OrderStatus { get; set; }

		public DateTime StatusDate { get; set; }

		public IStock StockForCover { get; set; }
		public IStock StockForCoverOverride { get; set; }
		public JobApprovalOptions SupplyArtworkApproval { get; set; }

		public bool Magnet { get; set; }

		public int Pages { get; set; }
		public bool Perforating { get; set; }
		public string PerforatingInstructions { get; set; }

		public decimal ThicknessOfSingleJob { get; set; }
		public int Quantity { get; set; }

		public bool IsAutoPriceExpired { get; set; }
		//bool IsBrochure { get; }
		//bool IsBusinessCard { get; }
		//bool IsCMYK { get; }
		//bool IsCustomerGood { get; }

		//bool IsDigital { get; }
		//bool IsFurtherProcessingRequired { get; }
		//bool IsFurtherProcessingRequiredForAnyJobInOrder { get; }
		//bool IsFurtherProcessingRequiredForOtherJobsInOrder { get; }
		//bool IsMagazine { get; }
		//bool IsPartOfMultiJobOrder { get; }
		public bool IsQuoteExpired { get; set; }

		public bool IsQuotePrice { get; set; }
		private bool IsReprint { get; set; }
		private bool IsReprintJobDispatch { get; set; }
		//bool IsTheLastJobInOrderBeingProcessed { get; }

		//bool JobsOrderIsInSingleRun { get; }

		//bool MailedComplete { get; set; }
		//bool MailedCompletePayment { get; set; }
		//bool MailedGoneFinish { get; set; }
		//bool MailedGonePlate { get; set; }
		//bool MailedPrePayment { get; set; }

		//string NCRNo { get; set; }
		public bool NeedApproval { get; }

		//JobStatusOptions NextStatus { get; set; }
		//int NumPressSheets { get; set; }
		public PadDirection PadDirection { get; set; }

		//string Preview { get; set; }

		public string PriceBase { get; set; }
		public DateTime PriceDate { get; set; }
		public decimal PriceMargin { get; set; }
		public string PriceMarginValue { get; set; }
		public bool Printed { get; set; }

		//DateTime PrintedDate { get; set; }

		public string ProductionInstructions { get; set; }
		//string ProductPriceCode { get; set; }

		//
		public bool QuoteNeedApprove { get; set; }

		//
		public JobApprovalOptions ReadyArtworkApproval { get; set; }

		//DateTime ReceivedDate { get; set; }
		public int ReOrderSourceJobId { get; set; }

		//string ReprintCost { get; set; }
		public int ReprintFromPreviousJobNo { get; set; }

		//int ReprintFromPreviousRunNo { get; set; }
		//string ReprintReason { get; set; }
		//string ReprintReasonPredefined { get; set; }
		//string ReprintResult { get; set; }
		//string RequestedPackaging { get; set; }

		#region staff only fields

		public DateTime? RequiredByDate { get; set; }
		public string LepSpecialInstructions { get; set; }
		public string MYOB { get; set; }

		public bool InvolvesOutwork { get; set; }
		public bool PreviewChk { get; set; }
		public bool ProofChk { get; set; }
		public bool OnHoldChk { get; set; }

		public List<int> RunIds { get; set; }

		#endregion staff only fields

		public int SuggestedSlot { get; set; }

		public string StatusRenderedForStaff { get; }
		public string StatusRenderedForCustomer { get; }

		public BrochureDistPackInfo BrochureDistPackInfo { get; set; } = new BrochureDistPackInfo() { };//MailHouse = new MailHouse()
		public int Copies { get; set; }

		public bool? AACNotPerformed { get; set; }

		public int? WLCustomerId { get; set; }
		public string WLAnonymousUserId { get; set; }



		#region  Quote Fields
		public string QuoteEstimator { get; set; }
		public decimal? QuoteCOGS { get; set; }
		public decimal? QuoteOutworkCost { get; set; }
		public string QuoteComments { get; set; }
		public string QuotePrimary { get; set; }
		public string QuoteOutcome { get; set; }

		public string QuoteLostReason { get; set; }
		public string QuoteLostComments { get; set; }


		public string QuoteFollowUpNotes { get; set; }
		public string QuoteFollowUpNotesAdd { get; set; }
		public int NumberOfMagnets { get; set; }
		#endregion



		public JobSplits Splits { get; set; }


		public bool HasSplitDelivery { get; set; }
		public decimal SpineWidth { get; set; }
		public void CreateMappings(Profile profile)
		{
			profile.CreateMap<Job, JobViewCustDto>()
			//.ForMember(d=>d.Stock, opt => opt.MapFrom(s=>s.Stock_))
			.AfterMap((s, d) =>
			{

				//d.Template.SizeOptions = null;
				d.RequiredPositions = (List<string>)s.GetRequiredPosition();

				d.ProofChk = s.Proofs.ProofsRequired;
				d.PreviewChk = s.ReadyArtworkApproval == JobApprovalOptions.NeedsApproval;
				d.OnHoldChk = s.ProofStatus == JobProofStatus.OnHold;
				d.RunIds = s.Runs.Select(r => r.Id).ToList();
				d.SuggestedSlot = s.CalculateJobSize();
				d.SpineWidth = s.GetSpineWidth();
				if (s.Order != null)
				{
					d.OrderId = s.Order.Id;
					if (s.Order.IsWLOrder)
					{
						d.IsWhiteLabel = true;
						d.WLCustomerId = s.Order.WLCustomerId;
						d.WLAnonymousUserId = s.Order.WLAnonymousUserId;
					}
				}

				d.FrontCelloglazeOverride = s.FrontCelloglazeOverride;
				d.BackCelloglazeOverride = s.BackCelloglazeOverride;
			});
		}
	}

	//public class AttachmentViewModel
	//{
	//	public Guid Id { get; set; }
	//	public string Name {get;set;}
	//	public long Size { get; set; }
	//}

}
