using System;
using lep;
using lep.configuration;
using lep.job;
using lep.job.impl;
using lep.order;
using lep.user;
using lep.user.impl;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NHibernate;
using NHibernate.Context;
using NHibernate.Engine;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using AutoMapper;
using lep.pricing;
using lep.pricing.impl;
using lep.promotion;
using lep.run;
using LepCore.Controllers;
using LepCore.Dto;
using LepCore.Setup;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using NHibernate.Criterion;
using NHibernate.Transform;
using Order = lep.order.impl.Order;
using Microsoft.AspNetCore.Http;
using lep.address.impl;
using System.Linq;
using lep.despatch.impl.label;
using lep.despatch.impl;
using lep.despatch;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Diagnostics;
using static NHibernate.Cfg.Environment;
namespace LepCore.Test
{

	[Serializable]
	public class UnitTestSessionContext : MapBasedSessionContext
	{

		public UnitTestSessionContext(ISessionFactoryImplementor factory) : base(factory)
		{
		}

		protected override IDictionary GetMap()
		{
			return items;
		}

		protected override void SetMap(IDictionary value)
		{
			items = value;
		}

		private IDictionary items;
	}

	[TestClass]

	public class DISetup
	{
		private readonly IServiceProvider _serviceProvider;

		public DISetup()

		{

			var bd = AppContext.BaseDirectory;


			var TestProjectDirectory = AppContext.BaseDirectory.Substring(0, AppContext.BaseDirectory.IndexOf("LepCore.Test"));
			var LepCoreDirectory = Path.Combine(TestProjectDirectory, "LepCore");
			var settingsFile = Path.Combine(LepCoreDirectory,
											$@"appsettings.{System.Environment.MachineName}.json");

			var builder = new ConfigurationBuilder()
			.AddJsonFile(settingsFile, optional: true, reloadOnChange: true);
			var configRoot = builder.Build();

			var services = new ServiceCollection();

			services.AddSingleton<IConfigurationRoot>(provider => configRoot);
			services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();

			var nhconfig = new NHibernate.Cfg.Configuration();

			nhconfig.SetProperty(Dialect, typeof(NHibernate.Dialect.MsSql2012Dialect).AssemblyQualifiedName);
			nhconfig.SetProperty(ConnectionDriver, typeof(NHibernate.Driver.MicrosoftDataSqlClientDriver).AssemblyQualifiedName);
			nhconfig.SetProperty(Isolation, "ReadCommitted");
			nhconfig.SetProperty(PrepareSql, "true");
			nhconfig.SetProperty(UseSecondLevelCache, "true");
			nhconfig.SetProperty(UseQueryCache, "true");

			nhconfig.SetProperty(Hbm2ddlAuto, "none");
			nhconfig.SetProperty(Hbm2ddlKeyWords, "none");

			var connstr = configRoot["Nhibernate:Con"];
			nhconfig.SetProperty(ConnectionString, connstr);
			nhconfig.SetProperty(CommandTimeout, "600");

			// call session context works for both HangFire and Web
			nhconfig.SetProperty(CurrentSessionContextClass, "call");

			nhconfig.SetProperty(DefaultBatchFetchSize, "128");
			nhconfig.SetProperty(BatchSize, "50");

			nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, "false");
			nhconfig.SetProperty(NHibernate.Cfg.Environment.FormatSql, "false");
			//nhConfigurationCache.SetProperty(Environment.CurrentSessionContextClass, typeof(UnitTestSessionContext).AssemblyQualifiedName);

			nhconfig.AddAssembly(typeof(ICustomerUser).Assembly);

			//config.AppendListeners(NHibernate.Event.ListenerType.PostUpdate, new[] { new AuditEventListener() });


			nhconfig.AppendListeners(NHibernate.Event.ListenerType.PostUpdate, new[] { new NhAuditEventListener() });
			nhconfig.AppendListeners(NHibernate.Event.ListenerType.PostInsert, new[] { new NhAuditEventListener() });
			nhconfig.AppendListeners(NHibernate.Event.ListenerType.PostDelete, new[] { new NhAuditEventListener() });

			//nhconfig.AppendListeners(NHibernate.Event.ListenerType.Flush, new[] { new AuditEventListener() });

			////config.SetInterceptor(new NoUpdateInterceptor());
			foreach (NHibernate.Mapping.PersistentClass persistentClass in nhconfig.ClassMappings)
			{
				persistentClass.DynamicUpdate = true;
			}


			services.AddSingleton<NHibernate.Cfg.Configuration>(nhconfig);
			services.AddSingleton<ISessionFactory>(s => s.GetRequiredService<NHibernate.Cfg.Configuration>().BuildSessionFactory());
			services.AddScoped(s => s.GetRequiredService<ISessionFactory>()
				.WithOptions().Interceptor(new NHDIInterceptor(s)).OpenSession());


			//==================================================

			//services.AddScoped<NHibernate.ISession>(x => x.GetService<AppSessionFactory>().OpenSession());





			services.AddLepDependencyInjection(configRoot);


			var lepProfile = new LepAutoMapperProfile(() => _serviceProvider);
			var configAm = new MapperConfiguration(cfg => cfg.AddProfile(lepProfile));
			var mapper = configAm.CreateMapper();
			services.AddSingleton<IMapper>(mapper);
			_serviceProvider = services.BuildServiceProvider();
		}



		[TestMethod]
		public void DIWireUpTest()
		{
			Assert.IsNotNull(_serviceProvider.GetRequiredService<IOrderApplication>());
			Assert.IsNotNull(_serviceProvider.GetRequiredService<IJobApplication>());
			Assert.IsNotNull(_serviceProvider.GetRequiredService<IConfigurationApplication>());
			Assert.IsNotNull(_serviceProvider.GetRequiredService<IUserApplication>());

			var mvcSA = _serviceProvider.GetRequiredService<lep.security.ISecurityApplication>();

			Assert.IsNotNull(_serviceProvider.GetRequiredService<lep.jobmonitor.impl.JobBoardDTOHelper>());

			//Assert.IsNotNull(mvcSA.userApp);
		}


		[TestMethod]
		public void CanQueryFavAddress()
		{
			var session = _serviceProvider.GetRequiredService<NHibernate.ISession>();

			var x = session.Query<DeliveryDetails>().Where(_ => _.CustomerId == 13728);

			Assert.IsTrue(x.Any());


		}



		// [TestMethod]
		//public void JobApplicationCanReturnMulitpleSpecSizes()
		//{
		//	var jobApp = _serviceProvider.GetRequiredService<IJobApplication>();
		//	var template = jobApp.GetJobTemplate("Brochures & Flyers");
		//	Assert.IsNotNull(template);
		//	Assert.IsTrue(template.SizeOptions.Count > 0);
		//}


		[TestMethod]
		public void CanUpdateUser()
		{
			var userApp = _serviceProvider.GetRequiredService<IUserApplication>();

			var user = (ICustomerUser)userApp.GetUser(13692);
			var name = "1320 " + DateTime.Now.ToString();
			user.Name = name;
			userApp.Save(user);
			var user2 = (ICustomerUser)userApp.GetUser(13692);
			Assert.IsTrue(user2.Name == name);
		}



		[TestMethod]
		public void CanPatchUser()
		{
			var userApp = _serviceProvider.GetRequiredService<IUserApplication>();
			var user = (ICustomerUser)userApp.GetUser(13692);

			var patchDoc = new JsonPatchDocument<ICustomerUser>();
			patchDoc.Add(o => o.Name, "asdf asdf");
			patchDoc.Add<string>(o => o.PostalAddress.Address1, "asdf asdf");
			patchDoc.ApplyTo(user);

			Assert.IsTrue(user.Name == "asdf asdf");
			Assert.IsTrue(user.PostalAddress.Address1 == "asdf asdf");

			//var name = "1320 " + DateTime.Now.ToString();
			//user.Name = name;
			//userApp.Save(user);
			//var user2 = (ICustomerUser)userApp.GetUser(13692);
			//Assert.IsTrue(user2.Name == name);
		}

		[TestMethod]
		public void CanPatchUser2()
		{
			var userApp = _serviceProvider.GetRequiredService<IUserApplication>();
			var user = (CustomerUser)userApp.GetUser(13692);

			string serialized = @"[
                                    {""op"":""replace"",""path"":""/Name"",""value"":""1320 PRINT TO FINISH 2""},
                                    {""op"":""replace"",""path"":""/PaymentTerms"",""value"":2},
                                    {""op"":""replace"",""path"":""/PostalAddress/Address3"",""value"":""4""},
                                    {""op"":""replace"",""path"":""/PostalAddress/Address2"",""value"":""4""},
                                    {""op"":""replace"",""path"":""/PostalAddress/Address1"",""value"":""15 McCOSKER STREET 4""},
                                    {""op"":""replace"",""path"":""/BillingAddress/Address3"",""value"":""4""},
                                    {""op"":""replace"",""path"":""/BillingAddress/Address2"",""value"":""4""},
                                    {""op"":""replace"",""path"":""/BillingAddress/Address1"",""value"":""PO Box 2166 4""}
                                  ]";
			/**/
			var patchDoc = JsonConvert.DeserializeObject<JsonPatchDocument>(serialized);
			patchDoc.ApplyTo(user);

			Assert.IsTrue(user.Name == "1320 PRINT TO FINISH 2");
			Assert.IsTrue(user.PostalAddress.Address1 == "15 McCOSKER STREET 4");

			//var name = "1320 " + DateTime.Now.ToString();
			//user.Name = name;
			//userApp.Save(user);
			//var user2 = (ICustomerUser)userApp.GetUser(13692);
			//Assert.IsTrue(user2.Name == name);
		}


		// [TestMethod]
		//public void CanGetJob4Dto ()
		//{
		//    var config = new MapperConfiguration(cfg => {
		//        cfg.CreateMap<IJobTemplate, TrimmedTemplate>().ForMember(d => d.SizeOptions, opt => opt.Ignore());
		//        cfg.CreateMap<IArtwork, ArtworkDto>();

		//        cfg.CreateMap<IJob, JobDto4>()
		//            .ForMember(d => d.Order, opt => opt.Ignore())
		//            .ForMember(d => d.Prepress, opt => opt.Ignore())
		//            .ForMember(d => d.Proofs, opt => opt.Ignore())
		//            .ForMember(d => d.Template, opt => opt.MapFrom(src => Mapper.Map<TrimmedTemplate>(src.Template)))
		//            .ForMember(d => d.Artworks, opt => opt.MapFrom(src => Mapper.Map<IList<IArtwork>, List<ArtworkDto>>(src.Artworks)));
		//    });

		//    IMapper mapper = config.CreateMapper();



		//    var JobApplication = _serviceProvider.GetRequiredService<IJobApplication>();

		//    var job = JobApplication.GetJob(811507);

		//    var dto = mapper.Map<JobDto4>(job);

		//    Assert.IsTrue(dto.Order == null);
		//    Assert.IsTrue(dto.Prepress == null);
		//    // Assert.IsTrue(dto.Proofs == null);
		//    Assert.IsTrue(dto.Template.SizeOptions == null);


		//}


		// create a test method


		[TestMethod]
		public void CanCreateOrder()
		{

			try
			{
				var userApplication = _serviceProvider.GetRequiredService<IUserApplication>();
				var jobApplication = _serviceProvider.GetRequiredService<IJobApplication>();
				var orderApplication = _serviceProvider.GetRequiredService<IOrderApplication>();
				var pricingEngine = _serviceProvider.GetRequiredService<IPricingEngine>();

				//	var icelr = userApplication.AttemptLogin("lepdemo", "password");
				var customer = userApplication.GetCustomerUser("lepdemo");

				var t = DateTime.Now.Ticks.ToString();

				var order = orderApplication.NewOrder(customer);

				order.PurchaseOrder = "test";
				order.GST = 10;


				var template = jobApplication.GetJobTemplate(JobTypeOptions.BusinessCard);

				var finishSize = jobApplication.GetPaperSize(1);
				var quantity = 500;
				var stock = jobApplication.GetStock(18);

				var job = order.NewJob("test", quantity, false, "", template, customer);
				job.Stock = stock;
				job.FinishedSize.PaperSize = finishSize;
				job.Name = "test";
				job.PrintType = PrintType.O;
				job.FrontPrinting = JobPrintOptions.Printed;

				job.BackPrinting = JobPrintOptions.Printed;

				job.FinishedSize.Height = job.FinishedSize.PaperSize.Size.Height;
				job.FinishedSize.Width = job.FinishedSize.PaperSize.Size.Width;

				var price = pricingEngine.PriceJob(job, new StringBuilder(), out quantity, "");
				job.SetPrice(customer, price, 0, false);
				//var myob = pricingEngine.FindMYOB(job, quantity);

				order.Status = OrderStatusOptions.Open;
				orderApplication.Save(order);





			}
			catch (Exception ex)
			{

				var m = ex.Message;
				throw;
			}
		}


		[TestMethod]
		public void CanCreateWiroOrder()
		{

			try
			{
				var userApplication = _serviceProvider.GetRequiredService<IUserApplication>();
				var jobApplication = _serviceProvider.GetRequiredService<IJobApplication>();
				var orderApplication = _serviceProvider.GetRequiredService<IOrderApplication>();
				var pricingEngine = _serviceProvider.GetRequiredService<IPricingEngine>();

				//	var icelr = userApplication.AttemptLogin("lepdemo", "password");
				var customer = userApplication.GetCustomerUser("lepdemo");

				var t = DateTime.Now.Ticks.ToString();

				var order = orderApplication.NewOrder(customer);

				order.PurchaseOrder = "test";
				order.GST = 10;


				var template = jobApplication.GetJobTemplate(JobTypeOptions.WiroMagazines);

				var finishSize = jobApplication.GetPaperSize(9);
				var quantity = 500;
				var stock = jobApplication.GetStock(18);

				var job = order.NewJob("test", quantity, false, "", template, customer);
				job.Stock = stock;
				job.FinishedSize.PaperSize = finishSize;
				job.Name = "test";
				job.PrintType = PrintType.D;
				job.FrontPrinting = JobPrintOptions.Printed;
				job.BackPrinting = JobPrintOptions.Printed;

				job.FinishedSize.Height = job.FinishedSize.PaperSize.Size.Height;
				job.FinishedSize.Width = job.FinishedSize.PaperSize.Size.Width;

				job.WiroInfo = new WiroMagazineInfo();

				job.StockForCover = stock;
				job.WiroInfo.InnerFrontStockForCover = (Stock)stock;
				job.WiroInfo.InnerBackStockForCover = (Stock)stock;

				//var price = pricingEngine.PriceJob(job, new StringBuilder(), out quantity, "");
				job.SetPrice(customer, 0, 0, false);
				//var myob = pricingEngine.FindMYOB(job, quantity);

				order.Status = OrderStatusOptions.Open;
				orderApplication.Save(order);





			}
			catch (Exception ex)
			{

				var m = ex.Message;
				throw;
			}
		}





		[TestMethod]
		public void DPCFurtherProcessing()
		{

			var e = _serviceProvider.GetRequiredService<PrintEngine>();


			e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1470610 });
			e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1452644 });
			e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1433372 });
			e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1284313 });
			e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1471247 });

			e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480598 });
			e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480593 });
			//e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480592 });
			//e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480576 });
			//e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480575 });
			//e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480543 });
			//e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480540 });


			e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480431 });
			e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480430 });
			//e.ProcessPrint(new PrintItem() { LabelType = LabelType.DPCProcessing, DataNumber = 1480429 });


			//var label = new DPCPocessingThumbnailLabel();

			//label.ConfigurationApplication = _serviceProvider.GetRequiredService<IConfigurationApplication>(); ;
			//label.PrinterAndTray = GetPrinterAndTrayFromLabelTypeAndProductionFacility(item.LabelType,
			//	label.Job.Facility);
			//label.PrintFileName = filename;
			//label.SetupPrintProperties();
			//label.Print();


		}



		[TestMethod]
		public void CanGetEarlyPrinttype()
		{

			//var templateId = 27;
			//var paperSizeId = 40; var stockId = 32;


			//var userApp = _serviceProvider.GetRequiredService<IUserApplication>();
			////var pps = Session
			////        .CreateCriteria(typeof(IJobOptionSpecSize))
			////        .Add(Restrictions.Eq("Template", Session.Load<IJobTemplate>(templateId)))
			////        .Add(Restrictions.Eq("PaperSize", Session.Load<IPaperSize>(paperSizeId)))
			////        .Add(Restrictions.Eq("Stock", Session.Load<IStock>(stockId)))

			////        .List<IJobOptionSpecSize>();


			//Assert.IsTrue(pps.Count >= 0);


		}




		[TestMethod]
		public void CanGetPrintType()
		{

			var templateId = 27;
			var paperSizeId = 40; var stockId = 32;


			var Session = _serviceProvider.GetRequiredService<NHibernate.ISession>();
			//var pps = Session
			//        .CreateCriteria(typeof(IJobOptionSpecSize))
			//        .Add(Restrictions.Eq("Template", Session.Load<IJobTemplate>(templateId)))
			//        .Add(Restrictions.Eq("PaperSize", Session.Load<IPaperSize>(paperSizeId)))
			//        .Add(Restrictions.Eq("Stock", Session.Load<IStock>(stockId)))

			//        .List<IJobOptionSpecSize>();

			IJobTemplate jobTemplate = null;
			IPaperSize paperSize = null;
			IJobOptionSpecStock specStock = null;
			IJobOptionSpecSize specSize = null;
			var pps = Session.QueryOver<IJobOptionSpecStock>(() => specStock)
				.JoinAlias(() => specStock.JobOptionSpecSize, () => specSize)
				.JoinAlias(() => specSize.JobTemplate, () => jobTemplate)
				.JoinAlias(() => specSize.PaperSize, () => paperSize)
				.Where(a => jobTemplate.Id == templateId && paperSize.Id == paperSizeId && a.Stock.Id == stockId)
				.Select(c => c.PrintType)
				.List<PrintType>();

			Assert.IsTrue(pps.Count >= 0);


		}




		// [TestMethod]
		public void canDeserialiseUpdateStaff()
		{
			var jsonStringData =
				"{ 'Role': 0,'Barcode':null,'IPAddress':'*************'," +
				"'OrderSearchCriteria':{ 'Id':24989,'IsSearchPanelOpen':true,'OrderStatus':null," +
				"'JobType':null,'Celloglaze':null,'Size':null,'Stock':null,'Customer':'','OrderNr':'','JobNr':''," +
				"'IsWaitingApproval':false,'IsWithdraw':false,'IsNewOrder':false,'IsOnlyUrgentOrder':false,'IsOnPrepay':false," +
				"'IsOnhold':false,'IsAwaitingPayment':false,'IsQuoteRequired':false,'IsCorrectedOrder':false," +
				"'IsOpenOrder':false,'IsNonBusinessCard':false,'Page':0,'Ordering':'-SubmissionDate'," +
				"'IsOrderWithDigitalJob':false,'SortField':null,'SortDir':null}," +
				"'RunSearchCriteria':{ 'Id':24989,'IsSearchPanelOpen':false,'Customer':'','OrderNr':'','JobNr':''," +
				"'RunNr':'','IsUrgent':false,'IsOnHold':false,'RunSearchOption':0,'Size':null,'Cello':null,'Side':null," +
				"'Stock':null,'RunStatus':'','Ordering':'','RunOrdering':'','OpenRun':'19548;20774;24496;26846;33359'," +
				"'Facility':0},'Id':24989,'Username':'adrian','Password':null," +
				"'HashPassword':'$2a$10$tfi2ulCSnsYnZgflpOcleOTFiW43.cIOeoixhQCewjOWl7WtwOd46'," +
				"'IsEnabled':true,'Email':'<EMAIL>','IsStaff':true," +
				"'FirstName':'Adrian','LastName':'Corr','Phone':'5','Mobile':'5'," +
				"'DateCreated':'2010-05-17T10:51:02','DateModified':'2015-06-11T13:37:05.9'," +
				"'AreaCode':'5','LastLogin':'0001-01-01T00:00:00'}";

			Staff srcStaff = JsonConvert.DeserializeObject<Staff>(jsonStringData);
			Assert.IsTrue(srcStaff != null);

			var _userApplication = _serviceProvider.GetRequiredService<IUserApplication>();
			var user = (Staff)_userApplication.GetUser(24989);


			var _mapper = _serviceProvider.GetService<IMapper>();

			user = _mapper.Map<Staff, Staff>(srcStaff, user);
			//user.OrderSearchCriteria = _mapper.Map<OrderSearchCriteria, OrderSearchCriteria>(c.OrderSearchCriteria, obj.OrderSearchCriteria);
			//user.RunSearchCriteria = _mapper.Map<RunSearchCriteria, RunSearchCriteria>(c.RunSearchCriteria, obj.RunSearchCriteria);
			_userApplication.Save(user);

			var user2 = (ICustomerUser)_userApplication.GetUser(24989);

		}


		[TestMethod]
		public void CanGetPromotion()
		{
			var _promoApp = _serviceProvider.GetRequiredService<IPromotionApplication>();
			var p = (IPromotion)_promoApp.GetPromotion(3);
			var _mapper = _serviceProvider.GetService<IMapper>();

			var result = _mapper.Map<PromotionDto>(p);

			Assert.IsTrue(result != null);
		}

		// [TestMethod]
		//public void NoHashPass ()
		//{
		//    var _promoApp = _serviceProvider.GetRequiredService<IPromotionApplication>();

		//    var _mapper = _serviceProvider.GetService<IMapper>();
		//    var _jobApplication = _serviceProvider.GetRequiredService<IJobApplication>();

		//    var job = _jobApplication.GetJob(809830);

		//    var jobDto = _mapper.Map<JobDto4>(job);
		//    Assert.IsTrue(string.IsNullOrEmpty(jobDto.Comments[0].Author.HashPassword));
		//}


		[TestMethod]
		public void JobSpecDto()
		{

			var _mapper = _serviceProvider.GetService<IMapper>();
			var _jobApplication = _serviceProvider.GetRequiredService<IJobApplication>();
			var _orderApp = _serviceProvider.GetRequiredService<IOrderApplication>();



			var job = _jobApplication.GetJob(808939);    // get job from job app
			var dto = _mapper.Map<JobPressDetailsViewDto>(job);      // map to JobPressDetailsViewDto

			//Assert.IsFalse(String.IsNullOrEmpty(dto.Order.CustomerName));


			// try mappying a OrderSummaryDtoWithJobs from job.Order
			var dtoOrder1 = _mapper.Map<OrderSummaryDtoWithJobs>(job.Order);

			var cname = job.Order.Customer.Name;

			//Assert.IsFalse(String.IsNullOrEmpty(dtoOrder1.CustomerName), "CustomerName is null");
			//Assert.IsFalse(String.IsNullOrEmpty(dto.OrderCustomerName));
			//Assert.IsFalse(String.IsNullOrEmpty(dto.Order.CustomerName));



			// load jobs order manually and try mapping agani
			var order = _orderApp.GetOrder(620605);
			var dtoOrder2 = _mapper.Map<OrderSummaryDtoWithJobs>(order);
			Assert.IsFalse(String.IsNullOrEmpty(dtoOrder2.CustomerName));
			Assert.IsTrue(dtoOrder2.Jobs.Count > 0);

		}


		[TestMethod]
		public void OrderSummaryDtoWithJobs()
		{

			var _mapper = _serviceProvider.GetService<IMapper>();
			var _orderApp = _serviceProvider.GetRequiredService<IOrderApplication>();

			var order = _orderApp.GetOrder(620605);
			var dtoOrder2 = _mapper.Map<OrderSummaryDtoWithJobs>(order);

			Assert.IsFalse(String.IsNullOrEmpty(dtoOrder2.CustomerName));
			Assert.IsTrue(dtoOrder2.Jobs.Count > 0);
		}

		[TestMethod]
		public void OrderDtoLarge()
		{

			var _mapper = _serviceProvider.GetService<IMapper>();
			var _orderApp = _serviceProvider.GetRequiredService<IOrderApplication>();

			var order = _orderApp.GetOrder(620605);
			var dtoOrder2 = _mapper.Map<OrderDtoLarge>(order);


			Assert.IsTrue(dtoOrder2.Jobs.Count > 0);
		}

		[TestMethod]
		public void JobSpecDtoWithTrimmedPaperSize()
		{
			var _mapper = _serviceProvider.GetService<IMapper>();
			var _orderApp = _serviceProvider.GetRequiredService<IOrderApplication>();
			var _jobApp = _serviceProvider.GetRequiredService<IJobApplication>();

			var job = _jobApp.GetJob(808939);    // get job from job app
			var dto = _mapper.Map<JobPressDetailsViewDto>(job);      // map to JobPressDetailsViewDto


			Assert.IsTrue(dto.Template.Id > 0, "  template id is 0!");
			Assert.IsTrue(dto.Stock.Id > 0, "  stock id is 0!");

			var dtoPS = _mapper.Map<PaperSizeDto>(job.FinishedSize);
			Assert.IsTrue(dtoPS.Id > 0, " id should not be 0");
			Assert.IsTrue(!String.IsNullOrEmpty(dtoPS.Name), " name should not be null");

			// manual reading from dto
			//var size = _mapper.Map<Size>(dtoPS);
			//Assert.IsTrue(size.PaperSize.Id > 0, "s id should not be 0");
			//Assert.IsTrue(!String.IsNullOrEmpty(size.PaperSize.Name), "s name should not be null");
		}


		[TestMethod]
		public void RunViewDto()
		{
			var _mapper = _serviceProvider.GetService<IMapper>();
			var _runApp = _serviceProvider.GetRequiredService<IRunApplication>();
			var run = _runApp.GetRun(225640);
			var runDto = _mapper.Map<RunViewDto>(run);

			Assert.IsTrue(runDto.Jobs.Count > 5, " run has jobs ");
			Assert.IsTrue(runDto.Jobs[0].Id != 0, " job has id ");
		}


		// [TestMethod]
		//public void RunViewDto2 ()
		//{
		//    var mConfig  = new AutoMapperConfig2( );
		//    var config = new MapperConfiguration(cfg => { cfg.AddProfile(mConfig); });
		//    var _mapper = config.CreateMapper();


		//    var _runApp = _serviceProvider.GetRequiredService<IRunApplication>();
		//    var run = _runApp.GetRun(225640);
		//    var runDto = _mapper.Map<RunViewDto>(run);

		//    Assert.IsTrue(runDto.Jobs.Count > 5, " run has jobs ");
		//    Assert.IsTrue(runDto.Jobs[0].Id != 0, " job has id ");




		//}

	}
}
