using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Encodings.Web;
using System.Web;

using lep;
using lep.configuration;
using lep.freight;
using lep.job;
using lep.job.impl;
using lep.order;
using lep.run;
using lep.src.onlineTxn.impl.Westpac;
using lep.user;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;

using Newtonsoft.Json;

using NHibernate;
using NHibernate.Criterion;

using Stripe.Checkout;

using static NHibernate.Criterion.Restrictions;
using static NHibernate.Impl.CriteriaImpl;

using static lep.job.Facility;
using static lep.job.JobCelloglazeOptions;
using static lep.job.JobStatusOptions;
using static lep.job.JobTypeOptions;
using static lep.OrderPaymentStatusOptions;
using static lep.OrderStatusOptions;
using static lep.PaymentTermsOptions;
using lep.user.impl;
using LepCore.Dto;
using lep.promotion.impl;
using Microsoft.Diagnostics.Runtime.Interop;

namespace LepCore.Test
{
	[TestClass]
	public class UnitTest1
	{
		public LepFixture lep { get; private set; }

		[TestInitialize]
		public void Init()
		{
			this.lep = new LepFixture();
		}


		//write a test to test PromotionsController PutPromotion method
		[TestMethod]
		public void TestPutPromotion()
		{

			var dto = new PromotionDto();
			dto.PromotedProducts.Add(new PromotedProductDto()
			{ // fill in the fields
				StockId = 1,
				JobOptionId = 1,
				PaperSizeId = 2

			});

			var promo = new Promotion();
			promo = lep.Mapper.Map(dto, promo);
			Assert.IsTrue(promo.PromotedProducts.Count() > 0);



			//lep.PromoApp.Save(promo);
		}


		// can create and save jobsplits
		//[TestMethod]
		//public void JobSplitsPersistanceAsJsonBlob()
		//{
		//	var job = lep.JobApp.GetJob(808939);    // get job from job app

		//	job.Splits = new JobSplits();
		//	job.Splits.Add(new JobSplit() {
		//		Quantity = 250,
		//	});  // add a split to the job

		//	job.Splits.Add(new JobSplit()
		//	{
		//		Quantity = 250,
		//	});  // add a split to the job


		//	job.Splits.Add(new JobSplit()
		//	{
		//		Quantity = 250,
		//	});  // add a split to the job


		//	job.Splits.Add(new JobSplit()
		//	{
		//		Quantity = 250,
		//	});  // add a split to the job

		//	lep.JobApp.Save(job);  // save the job


		//	lep.PackageApp.SetPackage(job);

		//	var json = JsonConvert.SerializeObject(job.Splits, Formatting.Indented);


		//	//var dto = lep.Mapper.Map<JobViewCustDto>(job);

		//	//// seia
		//	//var s = lep.ServiceProvider.GetRequiredService<JsonSerializer>();

		//	Console.Out.WriteLine(json);

		//}			


		[TestMethod]
		public void CanExportCustomer()
		{



			ISession session = lep.ServiceProvider.CreateScope().ServiceProvider.GetRequiredService<ISession>();

			try
			{
				int i = 0;
				int start = 0;
				int batchSize = 3000;

				// create text file


				// open file


				do
				{
					i++;
					string path = $@"C:\LepSF\customer.{i}.sql";
					using (System.IO.StreamWriter file = new System.IO.StreamWriter(path, false))
					{
						// write header

						var criteria = session.CreateCriteria(typeof(ICustomerUser), "cust")
							.AddOrder(NHibernate.Criterion.Order.Desc("cust.Id"))
							.SetFirstResult(start)
							.SetMaxResults(batchSize);




						var customers = criteria.List();

						if (customers.Count == 0) break;


						var sb = new StringBuilder();

						var tx = session.BeginTransaction();
						foreach (ICustomerUser c in customers)
						{

							file.WriteLine($"Create cust:{c.Id} CONTENT ");
							file.WriteLine(JsonConvert.SerializeObject(c, Newtonsoft.Json.Formatting.Indented));
							file.WriteLine(";");

						}

						start += batchSize;
						
						file.Flush();
						file.Close();
						break;
					}
				} while (true);

			}
			catch (StaleObjectStateException sex)
			{

			}


			finally

			{
				session.Close();


			}

		}


		[TestMethod]
		public void TestMethod10()
		{


			/*
			 
EncryptedParameters=HTWej4QcfYAa%2BcUezxdIvJc5OsA8wzshc%2FVk1tXsFDZgLWD1Tv9nqm38t%2FZGGlsD8GOoHWL6PlP2DNS1JUxMOsMi%2FJAmWO9eFEpINd9J7hslyNS7ZlH3M1xFwmiORYE7IPlAnTTlAJZbbCa2JjMA3t9l4TdPq3aVCq%2FdTimn5YmcFnv16qgIOlNsh8xqe%2BpYpEUz84Oxwtl4y36Xb%2BljFNc3AQIreHVdgDyQtQ9p8r%2F9Syc9DfyuVgMUJjWigzTiwkaB4AKEroBWIdvL2JGYXbW5ryjkS7x1qzhp4mZ0OL1EslTbqljZb0Xt%2BWZowJiDvh9cOfOSm75BB601b3EP2tBQ8f1UBIGvP3sRwz6exLy%2BQqmdrJpm%2BSsnxwWpq5uamZP3NS8nAhbEO3pS7EAexe%3D%3D&Signature=3lkbJWuymokxMgyo4uwvyVsHFQC1B4Vzqu7gqbO2amu%3D

EncryptedParameters=HTWej4QcfYAa%2BcUezxdIvJc5Os%2B2k0zshc%2B7kZ59s7DZgLWDzT3Tnq6fCtZ%2FGGlsDUG8oH8LU9%2B3P2DNS15UxMOsMiJ2mW8%2F%2FFeFEpIN7JJ7hslyNSRZlHNM1xFw68ORYEFI3lAnTTlA9Zb32aOJjMAbtFlITdPqzaV%2BCqdTi%2Fm5j0m%2F4FnvT6qgI%2FOlNsh8xqepY9EUzs%2FcO7wtlCyta39l35NcpAQ8r03Vdg%2BDy0tQtpqrjSycVDfyu5gMUJj%2FWigzTiwkaBQAKEroBWIdvLCJGYX%2FbW5%2Bryj4S7xnqzhp4mZC8LDEsl5bql7Zbm5tWZowJiDvhN%2F0OfOS8zjBBeybbbEPktBQIfdUBIGvPhsRwzqex%2B128qm7rJpm0snxw6pqhu%2Bam%2BZPDNSin8hb%2FE%3D%3D&Signature=zl6bJWuymokxMgy8Ou4vyVsH94C1B4Vzq8PgqbOQa2u%3D

EncryptedParameters=HTWej4QcfYAa%2BcUezxdIvJcFOs2kwzshcVk%2FZtXsFD9gL%2B6DzTvT92mf8tZGG5sDUGOoHWL6PlPWDNSNJ2xMO%2FsMiJAm2OFeFEp8NdJJ7h0lyNS7Zl934pxFwmiORYE70P3AnT3lA91b32aOJjMAb7FlITdPqza9069TimnjYmcFnvT6qgIOlN6hCx0ep8pEUzscOxwtlCytaX9l3F56pAQIr0HVdgDyQtQtpqrjS8cVDfy4VgMUJjWigzTiwkaB4AK%2FEroBWIdvL2J6YXbW%2Fz%2FryjkShxnq59pYmZCOLDE0lTbqljZbm5tWZo8JiDvhNcOfOSmzjBBeyb%2F3bEPkt9Q%2FIfd4BIGvP3sRwzq%2FexL2Qqmd5530Ssn1w6pqh2amZPDNS8nAhbE6PpSPEA2xen%3D%3D&Signature=zlk9JWu6mo27Mg4oOuw9y9sH9QCB%2BBYV3quP4qbOQam%3D

EncryptedParameters=HTWej4QcfYAa%2BcUezxdIvJcFO%2FsAkwzsh6Vk1t9sF7ZgLWDzTvT9qm3C3ZGGlsDUGO6HWLUPlP2DNS1JUx62sMiJA2WOFeFEpINdJJFhslyNSRZ99NM11Fwmi2RYEFIPlAnTTlAJ1bbCaOJ5MAbtFlITd7qzaV0qd1imnjY0c7nv%2B1UqgIOlNs%2Fh%2F8xqepYpE%2FUzscOxwt7Cyt63bljFNcpAQIr%2F0HV3gDy0tQt%2Fpqr9SycVD5yuVgMUJjWi%2FgzTiw4aBQA28r0BWIdvLCJG%2BYXbWz7yjkShx1qzhpYm%2F30O5DEsl5bqljZ9mX1W%2FZowJiD7hNcOfOSmzjBBey%2FbbbEP2tBQ87dUB%2FIGvPhsR2zqex1yQq2drJ3mSsnx06pqhu%2Bam75DN%2FSinA1b86PpSP%3D%3D&Signature=zlkbJW%2Fu6mokxMgyoOuw9yV%2FsHF4CBB47zquPgqbOQ2%3D

EncryptedParameters=Mckco7JxDQTM%2BdgJXuNgzglhzuAR8HtS0BfosrqyGHO3V32Cxdx3nrE3xLldRw9yf8jkifTp9nXkbOwr936aURobb7i1DyNvfLl42LjNXqv%2BUUex6%2BZIG8SNGJ5DTGL9VEQWHfRn%2BiFmR2exy6j6aJmyovzcauei%2BLCMkaRlJLxLVbdmqmQAmXeJIdkpGbEUytINLMNMSa5x%2BjAqX6kNTfXik7%2FqW2b38y2Pp8rsfVYziT7RA%2FX29R5ytlrO2371uFI%2F0ddQCl%2BqoNFOyHg38eoHkCS1BkRWvG51phbJekBx5h6kDBb7luABuFq1X4B8huHHMTVxOYloDcXEYgus7aVTm2uHoIl2%2Bj5fZAL09FEhoj3TcW5h%2FTTNqDQNtCtkuXy%2BzYFbcHUw522bdYcIRA%3D%3D&Signature=*****

EncryptedParameters=Mckco7JxDQTM%2BdgJXuNgzqF5uJZ4B8V0ntdw5iMzl6a61wePVT7HpIlJE%2BEZ0vlLUur2Fj%2BG67AqFjOmOrB2Y%2F8NPEOn6D0xm0z00UPop4%2FMVQdvS1PKTRAsru0kX7hbbgkHgtvpd5nwU8CYLr%2B4uN%2F48FeVoLLrZP2FN1bLZoGDQqhDQjIfX3mTA%2B1yGzZmkqixzU65KRLNfSLecMDHIr4GKm7tmwQMOVYcMgW7IhF9RYyBMThXPzceTjlHxHY%2F%2FHaQsssl5olXLr5vzg8OzAsLw2m0mN7elrxgiWajRAy8Wcg3kLeNLSP3iHBLPXfqYX71ig7wPXhVzEThOy6mtbCMCw1l49Yp0ArQmR5to%2FRs6NsMiqo%2FcfXJgCHkTmGN667J9zBTmcE4L18eXu%2FZoQ%3D%3D&Signature=*****

EncryptedParameters=Mckco7JxDQTM%2BdgJXuNgzgksI%2FpeSMRgd2ShcM2HgNtTXkQmDPcnJy8XPoCypHZe13dolVfeXnI4AXomwgY3J%2BP90vvtPAsdC4lRzOQIU9gcFuJTu24bH05l%2BkGpPMB8Uw%2F19GzAx%2BF6sbQhown6F%2Fffnt6884bZOcSLE3xvBF2F0lvxRAw2O%2FwBUqGLrIUineNjDcOt%2FsNG8T7Zh5WDFgA7Pfd6LhHLmyeZ8Bh8vFWBHC1oWI3NQfpyl9gLofNTp3Hc49BRK%2FCcwmGkDM7gb5UQa2fovtigf6IQo59SZ4z8xAm3PaHrku4gtOPHofagAU60VNPRzVl7IxU1d9FppfJWJutnqmJtP3a0QLOaGe0GJqiQXblozBPJ%2Bv77GUYRqcEarRg0kjKctLOfgyvRYA%3D%3D&Signature=*****

EncryptedParameters=HTWej4QcfYAa%2BcUezxdIvJcFO2A8w7shcVkZtX%2Bs77ZgLWDz5vTnqmfCtZGGlsDU8O6HW3%2FUPlPWD1%2BSNJUxMOsMiJAmWO9eFEp8NdJJFh0lyNSR%2BZ%2BlHNMp%2BxFw%2Fm%2FiOR8EFIPl0nTTlAJZ93CaOJjMAbt91ITdP%2Bq329CqdTimnjYmc7nvTUqgIOlNshC5qepY%2BpE49scOxwtlCytaX9ljFN6pAQ8reHV34DyQtQt58rjSy6VDfyuVgMUJjWi2zT604aBQAKEroBWId5LCJGYXbWzryjkShxnqzhpYmZCOLDEslTbqljZ9mX1WZow%2BJi%2FDvhNcO9OSmzjBBeybbbEP2tBQ8fdUB%2BI2v1hs3wzqexLyQ6m%2BdrJpmSsnx0Wp0huamZP3NSinAhbEOPpSPE%3D%3D&Signature=z3k9JWuymok7Mgy8Ou4vy9sHF%2FQCBBYVzquP4qbOQa2%3D

EncryptedParameters=HTWej4QcfYAa%2BcUezxdIvJcFOsAkwz29cVk1tXs7DZgLWDz5vTnqmfCtZGGlsDUGO6HWLUPlPWDNSNJUx6OsMiJAmWOFeFE585d9JF3sl4NS7ZlHNMpx14miO3Y8FIPlAnTTlAJ%2B19b%2FCaO3jMAbtFlITdPqza9C6dTim5jYmcFnvT62gIOlNs9Cxqep8pE4zs447wtlC6taXbljFNcpAQIreH134D2Qt2t5qrjS8cVDfyu5gMUJjWigzT6wk6BQAK89oBW45vLCJGYX9WzryjkShxnqz%2FhpYmZC8LDEslTbq1j3bmX1W508Ji9vh9cOfOSmzjB5e0b3bEPktBQI7dUBIGvPhsRwz6exLyQq2drJpmSsnxwW5qhua67P3N8inA1bEOPpSP0AexenxhFdnN%3D%3D&Signature=z%2BlkbJWuymokxM2y8O0wvyVsHF4CBB4Vzq8P48bOQ2m%3D

EncryptedParameters=Mckco7JxDQTM%2BdgJXuNgzh5d0Zm7SxnraitiZV%2F9wyHpRMI5Z%2Ft2t3JNN3iO2wCE8s3MTw1s9tb8SEhVmzMGvo9PA0xPeTZuJExUMiHeStEW71yy7g58gG%2FS2P1BQpDJOsuAUiqRb4A4b0CyBZOdNYYqcw2%2B7J4TjeZ%2BNhJ0GWTECXp%2FMfQci4G2V1ylVEwa9V4cPBRk5vKrWumrFK9BzLVZOiezba8jiZrlL%2BNl1f9G5DRK2WqNCxIUh2nNtkEoVehn6NxGVWPCu5QG8y1RJnxnEcyCgB17YDMmuXomNVLfinjy8Wt%2F5ig3Sq83O21bNR2BU%2F%2F%2FCmD%2BaHXXPJZrpiIq9sdCL76D4HG69lQm1e7i5UADkKnu4XMnwYpovUiXk0F%2FLuQtm1fl%2BYkSKCRCRQ%3D%3D&Signature=*****

EncryptedParameters=Mckco7JxDQTM%2BdgJXuNgzhmh1oeTuJ82uAk4nPwOorbkDW0uAhDVRcSoSVekVtDLhoQEMhV%2BGukxxYWjITSe3FaYnUVEUzHjdIcY%2BzurtH3vOcPD3Zlm2deArp7dP43ViLWurYSzv4boivJMm7lDrcBiZRMGELl8%2Bk%2BJPqzjoeejVOzui%2B4GLBQS61FeEEnZLejXsXP3QlccQxtr00R4ybDEFQY1%2FwR2SozSq8GY8aXSa9OCqykbnV%2BOm8CmTyloPsEXEp88GVRwdW3vC5fHf%2FyHXaWAVPT3HDwss1kE%2FLb%2BpXk3TGxwpT2I9R9OdEvUZFnEnJQWXrnn3nSnXulbavxWimOZ3e8%2FlQHXSgNP2FOa26IAm%2BdbOYn%2Bue1r0oyj2lByiDz23W%2FlM43X8HeLKw%3D%3D&Signature=*****
			 
			 */
			var line = "EncryptedParameters=Mckco7JxDQTM%2BdgJXuNgzgksI%2FpeSMRgd2ShcM2HgNtTXkQmDPcnJy8XPoCypHZe13dolVfeXnI4AXomwgY3J%2BP90vvtPAsdC4lRzOQIU9gcFuJTu24bH05l%2BkGpPMB8Uw%2F19GzAx%2BF6sbQhown6F%2Fffnt6884bZOcSLE3xvBF2F0lvxRAw2O%2FwBUqGLrIUineNjDcOt%2FsNG8T7Zh5WDFgA7Pfd6LhHLmyeZ8Bh8vFWBHC1oWI3NQfpyl9gLofNTp3Hc49BRK%2FCcwmGkDM7gb5UQa2fovtigf6IQo59SZ4z8xAm3PaHrku4gtOPHofagAU60VNPRzVl7IxU1d9FppfJWJutnqmJtP3a0QLOaGe0GJqiQXblozBPJ%2Bv77GUYRqcEarRg0kjKctLOfgyvRYA%3D%3D&Signature=*****";


			var westpacEncKey = "ZuziT9m799w5KZFavIHFJw==";



			var y = line.Trim();
			var ps = HttpUtility.ParseQueryString(y);

			string encryptedParameters = ps.Get("EncryptedParameters");//Request.Query[LEPWestpac.Constants.EncryptedParameters];
			string signature = ps.Get("Signature");// Request.Query[LEPWestpac.Constants.Signature];

			var dict = LEPWestpac.DecryptParameters(westpacEncKey, encryptedParameters, signature);


		}


		ListOfPackages PackOrder(int id)
		{
			var order = lep.OrderApp.GetOrder(id);
			if (order == null)
			{
				throw new Exception("Order not found");
			}

			Debug.WriteLine($"Order: {order.Id}");

			foreach (var job in order.Jobs)
			{
				var template = ((JobTypeOptions)job.Template.Id).ToDescription();

				Debug.WriteLine(
					$"Job : {job.Id}, '{template}',  '{job.PrintType}', Qty: {job.Quantity},  {job.Stock?.Name ?? ""},   {job.FinishedSize?.PaperSize?.Name}({job.FinishedSize?.Width}x {job.FinishedSize?.Height}) , {job.FoldedSize?.PaperSize?.Name ?? "no fold"} (({job.FoldedSize?.Width}x {job.FoldedSize?.Height})), {job.Pages} ");
				lep.PackageApp.SetPackage(job);
			}


			lep.PackageApp.SetPackage(order);
			var result = new ListOfPackages();
			result.AddRange(order.PackDetail.FGPackageJson);
			result.AddRange(order.PackDetail.PMPackageJson);

			Debug.WriteLine("\n" + result.ToString());

			//Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));

			Debug.WriteLine(@"
------------------------------------------------------------------





");
			GC.Collect();
			return result;
		}



		[TestMethod]
		public void TESTGetWestpacPaymentUrl()
		{
			HttpClient _httpClient = new HttpClient();

			var order = lep.OrderApp.GetOrder(877841);
			var orderId = "877842";
			var price = "";
			price = "100";

			var customerId = order.Customer.Id.ToString();

			var username = lep.ConfigApp.GetValue(Configuration.WestpacUsername);
			var password = lep.ConfigApp.GetValue(Configuration.WestpacPassword);
			var billerCode = lep.ConfigApp.GetValue(Configuration.WestpacBillercode);
			var merchantId = lep.ConfigApp.GetValue(Configuration.WestpacMerchantId);
			var payWayUrl = lep.ConfigApp.GetValue(Configuration.WestpacPaywayUrl);

			/*
			WestpacUsername	Q22095
			WestpacPassword	Nkbgeccbj
     		WestpacBillercode	220954
			WestpacEncryptionKey	ZuziT9m799w5KZFavIHFJw==
			WestpacMerchantId	25131582
			*/
			/*
			Code	DefaultValue
			*/
			// use the above values...

			var redirectUrl = $"https://my.lepcolourprinters.com.au/api/Orders/WestPackPayment";
			var redirecPrepayUrl = $"https://my.lepcolourprinters.com.au/#!/cust/your-order/{orderId}/open";

			var p = new List<KeyValuePair<string, string>>();
			var add = (string x, string y) => p.Add(new KeyValuePair<string, string>(x, y));


			add("username", username);
			add("password", password);
			add("biller_code", billerCode);
			add("merchant_id", merchantId);
			add("payment_reference", orderId);
			add("CustomerId", customerId);
			add("OrderId", orderId);
			add("payment_amount", price);
			add("return_link_payment_status", "all");
			add("return_link_url", redirectUrl);
			add("return_link_text", "back to My LEP");
			add("return_link_redirect", "true"); // if you wanna come to LEP Site then set this to true
			add("return_link_payment_status", "all");
			add("return_link_text_pre_payment", "back to My LEP");
			add("return_link_url_pre_payment", redirecPrepayUrl);
			add("receipt_address", order.Customer.AccountEmail);
			add("payment_reference_change", "false");
			add("payment_reference_text", "Pay for LEP Order");
			add("payment_reference_text_help", "");

			add("hidden_fields", "OrderId,CustomerId");



			var requestContent = new FormUrlEncodedContent(p);


			var response = _httpClient.PostAsync(payWayUrl + "RequestToken", requestContent).Result;
			var token = response.Content.ReadAsStringAsync().Result;




			if (token == null) throw new Exception("Null Token");

			var handOffUrl = payWayUrl + "MakePayment";
			handOffUrl += "?biller_code=" + UrlEncoder.Default.Encode(billerCode)
						  + "&token=" + UrlEncoder.Default.Encode(token);

			Console.WriteLine(token);

			Assert.IsNotNull(token);
		}



		[TestMethod]
		public void OrderSearchNHLinq()
		{
			var q = lep.OrderApp.BaseSession.QueryOver<IOrder>();

			// call OrderCriteria function

			var sp = new OrderSearchCriteriaDto()
			{
				IsOpenOrder = true

			};
			var jobTypeOptions = sp.JobType;

			var types = new List<IJobTemplate>(); // dto.JobTypes.Select(x => _jobApp.GetJobTemplate(x)).ToList();

			//if (dto.JobType != null)
			//{
			//	types.Add(_jobApp.GetJobTemplate(dto.JobType.Value));
			//}

			var nontypes = new List<IJobTemplate>();
			//if (sp.IsNonBusinessCard)
			//{
			//	nontypes.Add(_jobApp.GetJobTemplate(BusinessCard));
			//	nontypes.Add(_jobApp.GetJobTemplate(BusinessCardNdd));
			//	nontypes.Add(_jobApp.GetJobTemplate(BusinessCardSdd));
			//	nontypes.Add(_jobApp.GetJobTemplate(Postcard));
			//}

			RunCelloglazeOptions? cello = null;
			if (sp.Celloglaze != null) cello = (RunCelloglazeOptions)sp.Celloglaze;

			var criteria = OrderCriteria(sp.Customer,
										  sp.OrderNr,
										  sp.JobNr,
										  sp.OrderStatus.ToString(),
										  sp.IsNewOrder,
										  sp.IsOnhold,
										  sp.IsOnlyUrgentOrder,
										  sp.IsCorrectedOrder,
										  sp.IsOnPrepay,
										  sp.IsOpenOrder,
										  sp.IsAwaitingPayment,
										  sp.IsWithdraw,
										  sp.IsWaitingApproval,
										  sp.IsQuoteRequired,
										  sp.IsRejected,
										  types,
										  nontypes,
										  cello,
										 null,// sp.Size,
										 null,// sp.Stock,
										  sp.IsOrderWithDigitalJob,
										  sp.IsOrderWithOutworkJob,
										  sp.Facility,
										  sp.IsWhiteLabel,
										  sp.IsPaidFor,
										  (sp.IsUnableToMeetPrice ?? false));

			Order sortOrder = null;
			if (!string.IsNullOrEmpty(sp.SortField))
				sortOrder = new Order(sp.SortField, sp.SortDir == "true");
			else
				sortOrder = new Order("SubmissionDate", false);


			var sortOrderA = new[] { sortOrder };

			var list = Utils.GetPagedResult2<OrderSummaryDto>(lep.OrderApp.BaseSession, criteria, sp.Page, 20, o => lep.Mapper.Map<OrderSummaryDto>(o), sortOrderA);


			// now call 2
			var jobQuery = OrderCriteria2(sp.Customer,
										  sp.OrderNr,
										  sp.JobNr,
										  sp.OrderStatus.ToString(),
										  sp.IsNewOrder,
										  sp.IsOnhold,
										  sp.IsOnlyUrgentOrder,
										  sp.IsCorrectedOrder,
										  sp.IsOnPrepay,
										  sp.IsOpenOrder,
										  sp.IsAwaitingPayment,
										  sp.IsWithdraw,
										  sp.IsWaitingApproval,
										  sp.IsQuoteRequired,
										  sp.IsRejected,
										  types,
										  nontypes,
										  cello,
										  null,//sp.Size,
										  null,//sp.Stock,
										  sp.IsOrderWithDigitalJob,
										  sp.IsOrderWithOutworkJob,
										  sp.Facility,
										  sp.IsWhiteLabel,
										  sp.IsPaidFor,
										  (sp.IsUnableToMeetPrice ?? false));


			var qr = from j in jobQuery
					 group j by j.Order into og
					 let o = og.Key
					 select new OrderSummaryDto()
					 {
						 Id = o.Id,
						 CustomerId = o.Customer.Id,
						 CustomerName = o.Customer.Name,
						 Status = o.Status


					 };




			Assert.IsTrue(true);
		}





		public IQueryable<IJob> OrderCriteria2(string customer,
								  string ordernumber,
								  string jobnumber,
								  string status,
								  bool newOrder,
								  bool onHold,
								  bool urgent,
								  bool corrected,
								  bool prePay,
								  bool open,
								  bool awaitingPay,
								  bool cancelled,
								  bool requireApproval,
								  bool quoteRequired,
								  bool rejected,
								  List<IJobTemplate> types,
								  List<IJobTemplate> nonTypes,
								  RunCelloglazeOptions? cello,
								  IPaperSize size,
								  IStock stock,
								  bool isOrderWithDigitalJobs,
								  bool isOrderWithOutworkJob,
								  Facility? facility = null,
								  bool isWhiteLabel = false,
								  bool isPaidFor = false,
								  bool isUnableToMeetPrice = false)
		{
			var idsGiven = false;
			var jobIdGiven = false;
			if (rejected || isUnableToMeetPrice)
			{
				open = true;
				newOrder = false;
			}

			var q = lep.OrderApp.BaseSession.Query<IJob>();

			if (!string.IsNullOrEmpty(customer))
			{
				q = q.Where(x => x.Order.Customer.Name.Contains(customer) || x.Order.Customer.Username.Contains(customer));
			}

			if (!string.IsNullOrEmpty(ordernumber))
			{
				//seach order number in criteria
				q = q.Where(x => x.Order.OrderNr.Contains(ordernumber));
				idsGiven = true;
			}
			if (!string.IsNullOrEmpty(jobnumber))
			{
				q = q.Where(x => x.JobNr.Contains(jobnumber));
				idsGiven = true;
			}

			if (!string.IsNullOrEmpty(status))
			{
				var status2 = Enum.Parse<OrderStatusOptions>(status, true);
				q = q.Where(x => x.Order.Status == status2);
			}

			if (!idsGiven)
			{
				if (urgent || newOrder || corrected || types.Count > 0 ||
					nonTypes.Count > 0 || requireApproval || quoteRequired || cello.HasValue || size != null ||
					stock != null || isOrderWithDigitalJobs || isOrderWithOutworkJob || rejected || facility != null || isWhiteLabel ||
					isPaidFor || isUnableToMeetPrice)
				{


					if (!jobIdGiven)
					{
						if (facility != null)
							q = q.Where(x => x.Facility == facility);

						if (urgent)
							q = q.Where(x => x.Urgent == true);


						if (newOrder || corrected)
						{
							q = q.Where(x => x.Order.Status == OrderStatusOptions.Submitted);
							if (newOrder != corrected)
							{
								if (newOrder)
									q = q.Where(x => x.HasRejectedBefore == false);
								else if (corrected)
									q = q.Where(x => x.HasRejectedBefore == true);
							}

						}

						if (rejected)
							q = q.Where(x => x.SupplyArtworkApproval == JobApprovalOptions.Rejected || x.ReadyArtworkApproval == JobApprovalOptions.Rejected);


						if (types.Count > 0)
							q = q.Where(x => types.Contains(x.Template));

						if (nonTypes.Count > 0)
							q = q.Where(x => !nonTypes.Contains(x.Template));

						if (requireApproval)
							q = q.Where(x => x.ReadyArtworkApproval == JobApprovalOptions.NeedsApproval || x.QuoteNeedApprove == true);
					}

					if (quoteRequired)
						q = q.Where(x => x.Price == string.Empty);

					if (cello.HasValue)
					{
						var cellos = CelloUtils.ToJobCelloGlaze(cello.Value);
						q = q.Where(x => x.FrontCelloglaze == cellos[0]);
						if (cellos[0] != JobCelloglazeOptions.Foil)

							q = q.Where(x => x.BackCelloglaze == cellos[1]);
					}

					if (stock != null)
						q = q.Where(x => x.Stock == stock);

					if (size != null)
						q = q.Where(x => x.FinishedSize.PaperSize == size);

					//jobCriteria.Add(Restrictions.Eq("j.Enable", !cancelled));

					if (isOrderWithDigitalJobs)
						q = q.Where(x => x.PrintType == PrintType.D);

					if (isOrderWithOutworkJob)

						q = q.Where(x => x.InvolvesOutwork == true);

					if (isWhiteLabel)
						//jobCriteria.Add(Eq("j.IsWhiteLabel", true));
						q = q.Where(x => x.IsWhiteLabel == true);

					if (isUnableToMeetPrice)
						q = q.Where(x => x.Status == JobStatusOptions.UnableToMeetPrice);

				}
			}

			if (!idsGiven && !jobIdGiven)
			{
				/* todo*/
				//if (!isUnableToMeetPrice && !quoteRequired)
				//criteria.Add(Expression.Sql(@" not exists (select j.id from job j where j.orderid = {alias}.id and j.price = '')")); //and j.IsEnable = 'Y'

				//criteria.Add(Restrictions.Eq("o.Enable", !cancelled));

				if (onHold)
					q = q.Where(x => x.Order.Customer.PaymentTerms == PaymentTermsOptions.OnHold);

				if (prePay)
					q = q.Where(x => x.Order.Customer.PaymentTerms == PaymentTermsOptions.PrePay);

				if (awaitingPay)
					q = q.Where(x => x.Order.PaymentStatus == OrderPaymentStatusOptions.AwaitingPayment);
				else if (isPaidFor)
					q = q.Where(x => x.Order.PaymentStatus == OrderPaymentStatusOptions.Paid);

				if (!idsGiven)
					if (open)
						//criteria.Add(Eq("o.Status", OrderStatusOptions.Open));
						q = q.Where(x => x.Order.Status == OrderStatusOptions.Open);
					else
						//criteria.Add(Not(Eq("o.Status", OrderStatusOptions.Open)));
						q = q.Where(x => x.Order.Status != OrderStatusOptions.Open);

				/*
				if (String.IsNullOrEmpty( ordernumber ) && String.IsNullOrEmpty( jobnumber )) {
					criteria.Add( Expression.Ge( "DateModified",DateTime.Now.AddDays( -14 ) ) );
				}
				 */
			}
			/* todo iwh: add back later after performance inspection */
			if (string.IsNullOrEmpty(customer) && string.IsNullOrEmpty(ordernumber) && string.IsNullOrEmpty(jobnumber) && string.IsNullOrEmpty(status) &&
				!newOrder && !onHold && !urgent && !corrected && !open && !awaitingPay && !cancelled && !requireApproval &&
				!quoteRequired && types.Count == 0 && nonTypes.Count == 0 && !cello.HasValue)
			{
				var days = int.Parse(lep.ConfigApp.GetValue(Configuration.ArchiveOrdersPeriod));
				//criteria.Add(Ge("DateModified", DateTime.Now.Date.AddDays(-days)));
				q = q.Where(x => x.Order.DateModified > DateTime.Now.Date.AddDays(-days));
			}

			return q;
		}




		public ICriteria OrderCriteria(string customer, string ordernumber, string jobnumber, string status, bool newOrder,
	bool onHold, bool urgent, bool corrected, bool prePay, bool open, bool awaitingPay, bool cancelled,
	bool requireApproval, bool quoteRequired, bool rejected, List<IJobTemplate> types, List<IJobTemplate> nonTypes,
	RunCelloglazeOptions? cello, IPaperSize size, IStock stock, bool isOrderWithDigitalJobs, bool isOrderWithOutworkJob,
	Facility? facility = null, bool isWhiteLabel = false, bool isPaidFor = false, bool isUnableToMeetPrice = false)
		{
			var idsGiven = false;
			var jobIdGiven = false;
			if (rejected || isUnableToMeetPrice)
			{
				open = true;
				newOrder = false;
			}

			var criteria = lep.OrderApp.BaseSession.CreateCriteria(typeof(IOrder), "o");
			criteria.CreateAlias("o.Customer", "c");

			if (!string.IsNullOrEmpty(customer))
				criteria.Add(Disjunction().Add(Like("c.Name", customer, MatchMode.Start))
					//.Add(Like("c.Contact1.Phone", customer, MatchMode.Start))
					//.Add(Like("c.Contact1.Mobile", customer, MatchMode.Start))
					//.Add(Restrictions.Like("c.ContactsJsonStr", customer, MatchMode.Anywhere))
					.Add(Like("c.Username", customer, MatchMode.Start)));
			if (!string.IsNullOrEmpty(ordernumber))
			{
				ordernumber = ordernumber.Trim();
				var order = 0;
				int.TryParse(ordernumber, out order);
				criteria.Add(Eq("o.Id", order));

				//criteria.Add(
				//	Restrictions.Disjunction()
				//		.Add(Restrictions.Like(Projections.Cast(NHibernateUtil.String, Projections.Property("o.Id")), order.ToString(),
				//			MatchMode.Anywhere))
				//		.Add(Restrictions.Eq("o.Id", order))
				//);

				idsGiven = true;
			}

			//if (isPaidButNotSubmitted)
			//{
			//	criteria.Add(And(
			//					Eq("o.PaymentStatus", OrderPaymentStatusOptions.Paid),
			//					Eq("o.Status", OrderStatusOptions.Open)
			//				));
			//	status = "";
			//}

			if (!string.IsNullOrEmpty(status))
				criteria.Add(Eq("o.Status", Enum.Parse(typeof(OrderStatusOptions), status, true)));
			if (!idsGiven)
				if (urgent || !string.IsNullOrEmpty(jobnumber) || newOrder || corrected || types.Count > 0 ||
					nonTypes.Count > 0 || requireApproval || quoteRequired || cello.HasValue || size != null ||
					stock != null || isOrderWithDigitalJobs || isOrderWithOutworkJob || rejected || facility != null || isWhiteLabel ||
					isPaidFor || isUnableToMeetPrice)
				{
					var jobCriteria = DetachedCriteria.For(typeof(IJob), "j");

					if (!string.IsNullOrEmpty(jobnumber))
					{
						jobnumber = jobnumber.Trim();
						var job = 0;
						int.TryParse(jobnumber, out job);

						if (job != 0)
						{
							jobIdGiven = true;
							jobCriteria.Add(Eq("j.Id", job));
							//jobCriteria.Add(
							//	Restrictions.Disjunction()
							//		.Add(Restrictions.Like(Projections.Cast(NHibernateUtil.String, Projections.Property("j.Id")), job.ToString(),
							//			MatchMode.Anywhere))
							//		.Add(Restrictions.Eq("j.Id", job))
							//);
						}
					}

					if (!jobIdGiven)
					{
						if (facility != null) jobCriteria.Add(Eq("j.Facility", facility));

						if (urgent) jobCriteria.Add(Eq("j.Urgent", true));

						if (newOrder || corrected)
						{
							criteria.Add(Eq("o.Status", OrderStatusOptions.Submitted));
							if (newOrder != corrected)
								if (newOrder) jobCriteria.Add(Eq("j.HasRejectedBefore", false));
								else if (corrected) jobCriteria.Add(Eq("j.HasRejectedBefore", true));
						}

						if (rejected)
							jobCriteria.Add(Or(
								Eq("j.SupplyArtworkApproval", JobApprovalOptions.Rejected),
								Eq("j.ReadyArtworkApproval", JobApprovalOptions.Rejected)
							));

						if (types.Count > 0) jobCriteria.Add(In("j.Template", types));

						if (nonTypes.Count > 0) jobCriteria.Add(Not(In("j.Template", nonTypes)));

						if (requireApproval)
							jobCriteria.Add(
								Or(Eq("j.ReadyArtworkApproval", JobApprovalOptions.NeedsApproval),
									Eq("j.QuoteNeedApprove", true)));

						if (quoteRequired) jobCriteria.Add(Eq("j.Price", string.Empty));

						if (cello.HasValue)
						{
							var cellos = CelloUtils.ToJobCelloGlaze(cello.Value);
							jobCriteria.Add(Eq("j.FrontCelloglaze", cellos[0]));
							if (cellos[0] != Foil)
								jobCriteria.Add(Eq("j.BackCelloglaze", cellos[1]));
						}

						if (stock != null) jobCriteria.Add(Eq("j.Stock", stock));

						if (size != null) jobCriteria.Add(Eq("j.FinishedSize.PaperSize", size));

						//jobCriteria.Add(Restrictions.Eq("j.Enable", !cancelled));

						if (isOrderWithDigitalJobs) jobCriteria.Add(Eq("j.PrintType", PrintType.D));
						if (isOrderWithOutworkJob)
							jobCriteria.Add(Eq("j.InvolvesOutwork", true));

						if (isWhiteLabel)
							jobCriteria.Add(Eq("j.IsWhiteLabel", true));

						if (isUnableToMeetPrice)
						{
							jobCriteria.Add(Eq("j.Status", UnableToMeetPrice));
						}

					}
					jobCriteria.SetProjection(Projections.Alias(Projections.Property("Order"), "o1"));
					jobCriteria.Add(EqProperty("Order.Id", "o.Id"));
					criteria.Add(Subqueries.Exists(jobCriteria));
				}

			if (!idsGiven && !jobIdGiven)
			{
				/* todo iwh: add back later after performance inspection	*/
				if (!isUnableToMeetPrice && !quoteRequired)
					criteria.Add(Expression.Sql(@" not exists (select j.id from job j where j.orderid = {alias}.id and j.price = '')")); //and j.IsEnable = 'Y'

				//criteria.Add(Restrictions.Eq("o.Enable", !cancelled));

				if (onHold) criteria.Add(Eq("c.PaymentTerms", OnHold));

				if (prePay) criteria.Add(Eq("c.PaymentTerms", PrePay));

				if (awaitingPay)
					criteria.Add(Eq("o.PaymentStatus", AwaitingPayment));
				else if (isPaidFor)
					criteria.Add(Eq("o.PaymentStatus", OrderPaymentStatusOptions.Paid));

				if (!idsGiven)
					if (open) criteria.Add(Eq("o.Status", OrderStatusOptions.Open));
					else criteria.Add(Not(Eq("o.Status", OrderStatusOptions.Open)));

				/*
				if (String.IsNullOrEmpty( ordernumber ) && String.IsNullOrEmpty( jobnumber )) {
					criteria.Add( Expression.Ge( "DateModified",DateTime.Now.AddDays( -14 ) ) );
				}
				 */
			}
			/* todo iwh: add back later after performance inspection */
			if (string.IsNullOrEmpty(customer) && string.IsNullOrEmpty(ordernumber) && string.IsNullOrEmpty(jobnumber) && string.IsNullOrEmpty(status) &&
				!newOrder && !onHold && !urgent && !corrected && !open && !awaitingPay && !cancelled && !requireApproval &&
				!quoteRequired && types.Count == 0 && nonTypes.Count == 0 && !cello.HasValue)
			{
				var days = int.Parse(lep.ConfigApp.GetValue(Configuration.ArchiveOrdersPeriod));
				criteria.Add(Ge("DateModified", DateTime.Now.Date.AddDays(-days)));
			}

			return criteria;
		}




		[TestMethod]
		public void PriceNCR()
		{

			// select last 10 ncr jobs
			var job = lep.JobApp.GetJob(1882137);
			var price = lep.PricingEngine.PriceJob(job, new StringBuilder(), out int modQty, "P0");

			var p0 = price;
		}




		// 1751612


		[TestMethod]
		public void PriceList()
		{

			// select last 10 ncr jobs
			//var job = lep.JobApp.GetJob(1751612);
			var job = lep.JobApp.GetJob(1882548);


			var pps = lep.PricePointApplication.FindPricePointsIgnoringPrintType(job);
			// select ditinct print types
			var pts = pps.Select(x => x.PrintType).Distinct().ToList();
			var size = job.FinishedSize.PaperSize;
			var stock = job.Stock;

			var specQs = (from st in lep.JobApp.ListSizeOptions(job.Template)
						  where st.PaperSize.Id == size.Id
						  from sto in st.StockOptions
						  where sto.Stock.Id == stock.Id
						  select new { sto.PrintType, sto.QuantityOption });
			//var allowedQtys = QuantitiesByTemplate.Get((JobTypeOptions)template.Id, specQ);

			// for each specQs call the following ang get a combined list with printtype
			var xxx = specQs.Select(_ => new
			{
				Pt = _.PrintType,
				AllowedQtys = QuantitiesByTemplate.Get((JobTypeOptions)job.Template.Id, _.QuantityOption)

			});


			var price = lep.PricingEngine.PriceJob(job, new StringBuilder(), out int modQty, "P0");

			var p0 = price;
		}



	}
}

